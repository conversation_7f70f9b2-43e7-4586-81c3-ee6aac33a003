// Original V1 scraper service - restored
const { scrapeFunda } = require("./scrapers/fundaScraper");
const { scrapePararius } = require("./scrapers/parariusScraper");
const { scrapeHuurwoningen } = require("./scrapers/huurwoningenScraper");
const { cleanup, getScrapingMetrics } = require("./scraperUtils");

// Agent management
let agentStatus = {
  isRunning: false,
  currentTask: "idle",
  config: {
    scrapeInterval: 15 * 60 * 1000, // 15 minutes
    maxRetries: 3,
    timeout: 30000,
    activeScrapers: ["funda"],
  },
};

let agentInterval = null;

const getAgentStatus = () => {
  return { ...agentStatus };
};

const startAgent = async () => {
  if (agentStatus.isRunning) {
    console.log("Agent is already running");
    return { success: false, message: "Agent is already running" };
  }

  console.log("Starting scraping agent...");
  agentStatus.isRunning = true;
  agentStatus.currentTask = "scraping";

  // Start periodic scraping
  agentInterval = setInterval(async () => {
    try {
      console.log("🤖 Agent: Starting scheduled scrape...");
      await scrapeAll();
      console.log("✅ Agent: Scheduled scrape completed");
    } catch (error) {
      console.error("❌ Agent: Scheduled scrape failed:", error.message);
    }
  }, agentStatus.config.scrapeInterval);

  // Run initial scrape
  try {
    await scrapeAll();
  } catch (error) {
    console.error("❌ Initial scrape failed:", error.message);
  }

  return { success: true, message: "Agent started successfully" };
};

const stopAgent = () => {
  if (!agentStatus.isRunning) {
    console.log("Agent is not running");
    return { success: false, message: "Agent is not running" };
  }

  console.log("Stopping scraping agent...");
  agentStatus.isRunning = false;
  agentStatus.currentTask = "idle";

  if (agentInterval) {
    clearInterval(agentInterval);
    agentInterval = null;
  }

  return { success: true, message: "Agent stopped successfully" };
};

const updateAgentConfig = (newConfig) => {
  const oldConfig = { ...agentStatus.config };
  agentStatus.config = { ...agentStatus.config, ...newConfig };

  // If interval changed and agent is running, restart it
  if (
    agentStatus.isRunning &&
    oldConfig.scrapeInterval !== agentStatus.config.scrapeInterval
  ) {
    stopAgent();
    startAgent();
  }

  return {
    success: true,
    message: "Configuration updated",
    config: agentStatus.config,
  };
};

// Convenience function to scrape all sites
const scrapeAll = async () => {
  console.log("🚀 Starting scrape of all sites...");
  const results = [];

  if (agentStatus.config.activeScrapers.includes("funda")) {
    try {
      console.log("📍 Scraping Funda...");
      const fundaResult = await scrapeFunda();
      results.push({ site: "funda", success: true, data: fundaResult });
    } catch (error) {
      console.error("❌ Funda scraping failed:", error.message);
      results.push({ site: "funda", success: false, error: error.message });
    }
  }

  if (agentStatus.config.activeScrapers.includes("pararius")) {
    try {
      console.log("📍 Scraping Pararius...");
      const parariusResult = await scrapePararius();
      results.push({ site: "pararius", success: true, data: parariusResult });
    } catch (error) {
      console.error("❌ Pararius scraping failed:", error.message);
      results.push({ site: "pararius", success: false, error: error.message });
    }
  }

  if (agentStatus.config.activeScrapers.includes("huurwoningen")) {
    try {
      console.log("📍 Scraping Huurwoningen...");
      const huurwoningenResult = await scrapeHuurwoningen();
      results.push({
        site: "huurwoningen",
        success: true,
        data: huurwoningenResult,
      });
    } catch (error) {
      console.error("❌ Huurwoningen scraping failed:", error.message);
      results.push({
        site: "huurwoningen",
        success: false,
        error: error.message,
      });
    }
  }

  console.log("✅ All sites scraping completed");
  return results;
};

// Health check function
const healthCheck = () => {
  return {
    status: "healthy",
    timestamp: new Date().toISOString(),
    agent: {
      isRunning: agentStatus.isRunning,
      currentTask: agentStatus.currentTask,
    },
    scrapers: {
      available: ["funda", "pararius", "huurwoningen"],
      active: agentStatus.config.activeScrapers,
    },
  };
};

// Export functions
module.exports = {
  // Core scraping functions
  scrapeFunda,
  scrapePararius,
  scrapeHuurwoningen,
  scrapeAll,

  // Agent management
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,

  // Utility functions
  cleanup,
  getScrapingMetrics,
  healthCheck,

  // Legacy compatibility
  getAgentMetrics: getScrapingMetrics,
};
