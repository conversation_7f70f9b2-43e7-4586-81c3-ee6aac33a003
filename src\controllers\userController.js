const User = require("../models/User");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const config = require("../config/config");
const { catchAsync, AppError } = require("../middleware/errorHandler");
const tenantScoringService = require("../services/tenantScoringService");
const multer = require("multer");
const path = require("path");
const fs = require("fs").promises;

// Get current authenticated user (GET /api/auth/me)
exports.getMe = catchAsync(async (req, res, next) => {
  // req.user is set by the auth middleware
  const user = await User.findById(req.user._id).select("-password");
  if (!user) {
    return next(new AppError("User not found", 404));
  }
  res.status(200).json({
    status: "success",
    user,
  });
});

// Helper function to create JWT token
const signToken = (id) => {
  return jwt.sign({ _id: id }, config.jwtSecret, {
    expiresIn: config.jwtExpiresIn,
  });
};

// Helper function to create and send token response
const createSendToken = (user, statusCode, res) => {
  const token = signToken(user._id);

  // Remove password from output
  user.password = undefined;

  res.status(statusCode).json({
    status: "success",
    token,
    data: {
      user,
    },
  });
};

exports.register = catchAsync(async (req, res, next) => {
  try {
    const { email, password, firstName, lastName, phoneNumber, isPropertyOwner } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError("User with this email already exists", 400));
    }

    // Set role based on isPropertyOwner flag
    const role = isPropertyOwner ? 'owner' : 'user';
    
    // Log registration details for debugging
    console.log('Registering new user:', { 
      email, 
      firstName, 
      lastName,
      phoneNumber,
      isPropertyOwner, 
      role 
    });

    // Create user with appropriate role and profile information
    const userData = { 
      email, 
      password,
      role,
      profile: {
        firstName,
        lastName
      }
    };
    
    // Add phone number to profile if provided
    if (phoneNumber) {
      userData.profile.phoneNumber = phoneNumber;
    }
    
    // Initialize propertyOwner object if this is a property owner
    if (isPropertyOwner) {
      userData.propertyOwner = {
        isPropertyOwner: true,
        properties: [],
        verificationStatus: 'pending'
      };
    }

    console.log('Creating user with data:', JSON.stringify(userData, null, 2));
    
    const user = await User.create(userData);
    console.log('User created successfully with ID:', user._id, 'and role:', user.role);

    createSendToken(user, 201, res);
  } catch (error) {
    console.error('Registration error details:', error);
    // Check if this is a validation error
    if (error.name === 'ValidationError') {
      console.error('Validation error fields:', Object.keys(error.errors).join(', '));
      Object.keys(error.errors).forEach(field => {
        console.error(`Field ${field} error:`, error.errors[field].message);
      });
      return next(new AppError(`Validation error: ${error.message}`, 400));
    }
    // Pass the error to the global error handler
    return next(error);
  }
});

exports.login = catchAsync(async (req, res, next) => {
  const { email, password } = req.body;

  // Check if user exists and password is correct
  const user = await User.findOne({ email }).select("+password");

  if (!user || !(await bcrypt.compare(password, user.password))) {
    return next(new AppError("Incorrect email or password", 401));
  }

  createSendToken(user, 200, res);
});

exports.updatePreferences = catchAsync(async (req, res, next) => {
  const { userId } = req.params;
  
  // Extract preferences and profile from request body
  // Handle both direct properties and nested properties
  const receivedPreferences = req.body.preferences || req.body;
  const profile = req.body.profile;

  // Log the received data for debugging
  console.log('Updating preferences for user:', userId);
  console.log('Received preferences data:', JSON.stringify(receivedPreferences, null, 2));
  
  // First, get the current user to preserve any existing preferences
  const currentUser = await User.findById(userId);
  if (!currentUser) {
    return next(new AppError("No user found with that ID", 404));
  }
  
  // Start with existing preferences or empty object
  const existingPreferences = currentUser.preferences || {};
  
  // Create a new preferences object that includes both old and new fields
  const preferences = {
    ...existingPreferences,
    
    // Store the received preferences directly (new fields)
    minPrice: receivedPreferences.minPrice,
    maxPrice: receivedPreferences.maxPrice,
    minRooms: receivedPreferences.minRooms,
    maxRooms: receivedPreferences.maxRooms,
    preferredLocations: receivedPreferences.preferredLocations,
    propertyTypes: receivedPreferences.propertyTypes,
    amenities: receivedPreferences.amenities,
    notifications: receivedPreferences.notifications,
    
    // Also map to legacy fields for backward compatibility
    location: receivedPreferences.preferredLocations ? receivedPreferences.preferredLocations[0] : existingPreferences.location,
    budget: receivedPreferences.maxPrice || existingPreferences.budget,
    rooms: receivedPreferences.maxRooms || existingPreferences.rooms,
    propertyType: receivedPreferences.propertyTypes ? receivedPreferences.propertyTypes[0] : existingPreferences.propertyType,
    preferredNeighborhoods: receivedPreferences.preferredLocations || existingPreferences.preferredNeighborhoods || [],
  };
  
  // Build update object
  const updateData = { preferences };
  if (profile) updateData.profile = profile;

  // Log the update data being applied
  console.log('Update data being applied:', JSON.stringify(updateData, null, 2));

  // Use $set to update specific fields without overwriting the entire document
  const user = await User.findByIdAndUpdate(
    userId, 
    { $set: updateData },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!user) {
    return next(new AppError("No user found with that ID", 404));
  }

  // Log the updated user for verification
  console.log('User updated successfully. New preferences:', JSON.stringify(user.preferences, null, 2));

  res.status(200).json({
    status: "success",
    data: {
      user,
    },
  });
});

// Configure multer for profile picture uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/profile-pictures');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate unique filename with user ID and timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `${req.user._id}-${uniqueSuffix}${extension}`);
  }
});

const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new AppError('Only image files are allowed', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: fileFilter
});

// Middleware for single profile picture upload
exports.uploadProfilePicture = upload.single('profilePicture');

// Complete user profile (Requirements 1.1, 3.1, 3.2, 5.1, 5.2)
exports.completeProfile = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const {
    firstName,
    lastName,
    dateOfBirth,
    nationality,
    phoneNumber,
    userType,
    employment,
    rentalHistory,
    socialPreferences
  } = req.body;

  // Validate required fields for profile completion
  if (!firstName || !lastName || !userType || !Array.isArray(userType) || userType.length === 0) {
    return next(new AppError('First name, last name, and at least one user type are required', 400));
  }

  // Build profile update object
  const profileUpdate = {
    'profile.firstName': firstName,
    'profile.lastName': lastName,
    'profile.userType': userType
  };

  // Add optional fields if provided
  if (dateOfBirth) profileUpdate['profile.dateOfBirth'] = new Date(dateOfBirth);
  if (nationality) profileUpdate['profile.nationality'] = nationality;
  if (phoneNumber) profileUpdate['profile.phoneNumber'] = phoneNumber;
  if (employment) profileUpdate['profile.employment'] = employment;
  if (rentalHistory) profileUpdate['profile.rentalHistory'] = rentalHistory;
  if (socialPreferences) profileUpdate['profile.socialPreferences'] = socialPreferences;

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: profileUpdate },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Trigger tenant score recalculation if employment or rental history was updated
  let tenantScore = null;
  if (employment || rentalHistory) {
    try {
      tenantScore = await tenantScoringService.updateScore(userId);
    } catch (error) {
      console.error('Error updating tenant score:', error);
      // Don't fail the request if score calculation fails
    }
  }

  res.status(200).json({
    status: 'success',
    message: 'Profile completed successfully',
    data: {
      user,
      isProfileComplete: user.isProfileComplete,
      tenantScore: tenantScore
    }
  });
});

// Update user type (Requirements 3.1, 3.2)
exports.updateUserType = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { userType } = req.body;

  // Validate user type
  if (!Array.isArray(userType) || userType.length === 0) {
    return next(new AppError('User type must be a non-empty array', 400));
  }

  const validUserTypes = ['student', 'expat', 'young_professional', 'property_owner'];
  const invalidTypes = userType.filter(type => !validUserTypes.includes(type));

  if (invalidTypes.length > 0) {
    return next(new AppError(`Invalid user types: ${invalidTypes.join(', ')}`, 400));
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'profile.userType': userType } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    message: 'User type updated successfully',
    data: {
      user,
      userType: user.profile.userType
    }
  });
});

// Set language preference (Requirements 4.1, 4.2)
exports.setLanguagePreference = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { language } = req.body;

  // Validate language
  const supportedLanguages = ['dutch', 'english', 'arabic', 'turkish', 'polish'];
  if (!language || !supportedLanguages.includes(language)) {
    return next(new AppError(`Language must be one of: ${supportedLanguages.join(', ')}`, 400));
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'aiSettings.preferredLanguage': language } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    message: 'Language preference updated successfully',
    data: {
      user,
      preferredLanguage: user.aiSettings.preferredLanguage
    }
  });
});

// Upload and update profile picture (Requirements 5.1, 5.2)
exports.updateProfilePicture = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  if (!req.file) {
    return next(new AppError('No file uploaded', 400));
  }

  // Get the relative path for storing in database
  const relativePath = `/uploads/profile-pictures/${req.file.filename}`;

  // Update user's profile picture path
  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'profile.profilePicture': relativePath } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    // Clean up uploaded file if user not found
    try {
      await fs.unlink(req.file.path);
    } catch (error) {
      console.error('Error cleaning up uploaded file:', error);
    }
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    message: 'Profile picture updated successfully',
    data: {
      user,
      profilePicture: user.profile.profilePicture
    }
  });
});

// Get user profile completion status
exports.getProfileStatus = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  const user = await User.findById(userId).select('-password');
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check what profile fields are missing
  const missingFields = [];
  if (!user.profile?.firstName) missingFields.push('firstName');
  if (!user.profile?.lastName) missingFields.push('lastName');
  if (!user.profile?.userType || user.profile.userType.length === 0) missingFields.push('userType');
  if (!user.profile?.employment?.occupation) missingFields.push('employment.occupation');

  res.status(200).json({
    status: 'success',
    data: {
      isProfileComplete: user.isProfileComplete,
      missingFields,
      completionPercentage: Math.round(((4 - missingFields.length) / 4) * 100),
      profile: user.profile
    }
  });
});

// Update employment information
exports.updateEmployment = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { employment } = req.body;

  if (!employment) {
    return next(new AppError('Employment information is required', 400));
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'profile.employment': employment } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Trigger tenant score recalculation since employment affects scoring
  let tenantScore = null;
  try {
    tenantScore = await tenantScoringService.updateScore(userId);
  } catch (error) {
    console.error('Error updating tenant score:', error);
    // Don't fail the request if score calculation fails
  }

  res.status(200).json({
    status: 'success',
    message: 'Employment information updated successfully',
    data: {
      user,
      employment: user.profile.employment,
      tenantScore: tenantScore
    }
  });
});

// Update social preferences
exports.updateSocialPreferences = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { socialPreferences } = req.body;

  if (!socialPreferences) {
    return next(new AppError('Social preferences are required', 400));
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'profile.socialPreferences': socialPreferences } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    message: 'Social preferences updated successfully',
    data: {
      user,
      socialPreferences: user.profile.socialPreferences,
      isEligibleForSocialMatching: user.isEligibleForSocialMatching
    }
  });
});

// Update rental history information
exports.updateRentalHistory = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { rentalHistory } = req.body;

  if (!rentalHistory) {
    return next(new AppError('Rental history information is required', 400));
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { $set: { 'profile.rentalHistory': rentalHistory } },
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Trigger tenant score recalculation since rental history affects scoring
  let tenantScore = null;
  try {
    tenantScore = await tenantScoringService.updateScore(userId);
  } catch (error) {
    console.error('Error updating tenant score:', error);
    // Don't fail the request if score calculation fails
  }

  res.status(200).json({
    status: 'success',
    message: 'Rental history updated successfully',
    data: {
      user,
      rentalHistory: user.profile.rentalHistory,
      tenantScore: tenantScore
    }
  });
});

// Get tenant score
exports.getTenantScore = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  try {
    const scoreData = await tenantScoringService.calculateTenantScore(userId);
    
    res.status(200).json({
      status: 'success',
      data: {
        tenantScore: scoreData
      }
    });
  } catch (error) {
    return next(new AppError('Error calculating tenant score: ' + error.message, 500));
  }
});

// Get detailed tenant score breakdown
exports.getTenantScoreBreakdown = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  try {
    const breakdown = await tenantScoringService.getScoreBreakdown(userId);
    
    res.status(200).json({
      status: 'success',
      data: {
        breakdown: breakdown
      }
    });
  } catch (error) {
    return next(new AppError('Error getting score breakdown: ' + error.message, 500));
  }
});

// Generate tenant score report
exports.generateTenantScoreReport = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  try {
    const report = await tenantScoringService.generateScoreReport(userId);
    
    res.status(200).json({
      status: 'success',
      data: {
        report: report
      }
    });
  } catch (error) {
    return next(new AppError('Error generating score report: ' + error.message, 500));
  }
});

// Change user password
exports.changePassword = catchAsync(async (req, res, next) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user._id;

  // Validate input
  if (!currentPassword || !newPassword) {
    return next(new AppError('Current password and new password are required', 400));
  }

  if (newPassword.length < 6) {
    return next(new AppError('New password must be at least 6 characters long', 400));
  }

  try {
    // Get user with password
    const user = await User.findById(userId).select('+password');
    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Check if current password is correct
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return next(new AppError('Current password is incorrect', 400));
    }

    // Check if new password is different from current password
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      return next(new AppError('New password must be different from current password', 400));
    }

    // Update password (let the pre-save middleware handle hashing)
    user.password = newPassword;
    
    // Update security fields if they exist
    if (user.security) {
      user.security.lastPasswordChange = new Date();
    }

    await user.save();

    res.status(200).json({
      status: 'success',
      message: 'Password changed successfully'
    });
  } catch (error) {
    return next(new AppError('Error changing password: ' + error.message, 500));
  }
});
