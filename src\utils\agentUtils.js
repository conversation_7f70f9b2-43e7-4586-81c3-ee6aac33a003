/**
 * Calculates the autonomy level based on various metrics
 * @param {Object} metrics - Scraping metrics object
 * @returns {number} Autonomy level (0-100)
 */
const calculateAutonomyLevel = (metrics = {}) => {
  let level = 0;
  
  // Success rate component (up to 40 points)
  if (metrics.successRate > 90) level += 40;
  else if (metrics.successRate > 70) level += 30;
  else if (metrics.successRate > 50) level += 20;
  else level += 10;

  // Response time component (up to 30 points)
  if (metrics.averageScrapingTime < 5000) level += 30;
  else if (metrics.averageScrapingTime < 10000) level += 20;
  else level += 10;

  // Uptime component (up to 20 points)
  if (metrics.uptime > 0.95) level += 20;
  else if (metrics.uptime > 0.8) level += 15;
  else level += 5;

  // Error rate component (up to 10 points)
  const errorRate = metrics.failedScrapes / (metrics.totalScrapes || 1);
  if (errorRate < 0.1) level += 10;
  else if (errorRate < 0.3) level += 5;

  return Math.min(100, level);
};

/**
 * Calculates the autonomy rate based on successful operations
 * @param {Object} metrics - Scraping metrics object
 * @returns {number} Autonomy rate (0-100)
 */
const calculateAutonomyRate = (metrics = {}) => {
  if (!metrics.totalScrapes || metrics.totalScrapes === 0) return 0;
  return Math.round((metrics.successfulScrapes / metrics.totalScrapes) * 100);
};

/**
 * Calculates the success rate based on successful and failed operations
 * @param {number} successful - Number of successful operations
 * @param {number} failed - Number of failed operations
 * @returns {number} Success rate (0-100)
 */
const calculateSuccessRate = (successful, failed) => {
  const total = successful + failed;
  return total > 0 ? Math.round((successful / total) * 100) : 0;
};

/**
 * Formats the agent status for the API response
 * @param {Object} metrics - Scraping metrics
 * @param {boolean} isActive - Whether the agent is active
 * @param {string} currentTask - Current task description
 * @returns {Object} Formatted agent status
 */
const formatAgentStatus = (metrics, isActive, currentTask = 'idle') => {
  const now = new Date().toISOString();
  const autonomyLevel = calculateAutonomyLevel(metrics);
  const autonomyRate = calculateAutonomyRate(metrics);
  const successRate = calculateSuccessRate(
    metrics.successfulScrapes || 0,
    metrics.failedScrapes || 0
  );

  return {
    isActive,
    lastActivity: metrics.lastScrapeTime || now,
    currentTask,
    autonomyLevel,
    performance: {
      totalApplications: metrics.totalListingsFound || 0,
      successRate,
      averageResponseTime: metrics.averageScrapingTime || 0,
      autonomyRate,
      userSatisfactionScore: 0, // Placeholder for future implementation
    },
    lastUpdated: now,
  };
};

/**
 * Validates the agent configuration
 * @param {Object} config - Configuration object to validate
 * @returns {Object} Validation result { valid: boolean, errors: string[] }
 */
const validateAgentConfig = (config) => {
  const errors = [];
  
  if (!config) {
    return { valid: false, errors: ['No configuration provided'] };
  }

  // Validate scrape interval
  if (config.scrapeInterval !== undefined) {
    const interval = parseInt(config.scrapeInterval, 10);
    if (isNaN(interval) || interval < 300000 || interval > 86400000) {
      errors.push('Scrape interval must be between 5 minutes and 24 hours');
    }
  }

  // Validate max retries
  if (config.maxRetries !== undefined) {
    const retries = parseInt(config.maxRetries, 10);
    if (isNaN(retries) || retries < 0 || retries > 10) {
      errors.push('Max retries must be between 0 and 10');
    }
  }

  // Validate timeout
  if (config.timeout !== undefined) {
    const timeout = parseInt(config.timeout, 10);
    if (isNaN(timeout) || timeout < 10000 || timeout > 300000) {
      errors.push('Timeout must be between 10 and 300 seconds');
    }
  }

  // Validate active scrapers
  if (config.activeScrapers && Array.isArray(config.activeScrapers)) {
    const validScrapers = ['pararius', 'funda', 'huurwoningen'];
    const invalidScrapers = config.activeScrapers.filter(
      scraper => !validScrapers.includes(scraper)
    );
    
    if (invalidScrapers.length > 0) {
      errors.push(`Invalid scrapers: ${invalidScrapers.join(', ')}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

module.exports = {
  calculateAutonomyLevel,
  calculateAutonomyRate,
  calculateSuccessRate,
  formatAgentStatus,
  validateAgentConfig,
};
