const puppeteer = require("puppeteer");
const { logger } = require("./logger");

/**
 * AntiDetectionSystem - Comprehensive browser stealth and anti-detection service
 * Implements various techniques to avoid detection by automated systems
 */
class AntiDetectionSystem {
  constructor() {
    this.stealthProfiles = this.generateStealthProfiles();
    this.sessionData = new Map();
    this.captchaDetectionPatterns = [
      "captcha",
      "recaptcha",
      "hcaptcha",
      "cloudflare",
      "verification",
      "security check",
      "Je bent bijna op de pagina die je zoekt",
      "We houden ons platform graag veilig",
      "verifiëren dat onze bezoekers echte mensen zijn",
      "<EMAIL>",
    ];
  }

  /**
   * Generate multiple stealth profiles for browser fingerprint randomization
   */
  generateStealthProfiles() {
    const userAgents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    ];

    const viewports = [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
      { width: 1440, height: 900 },
      { width: 1536, height: 864 },
      { width: 1280, height: 720 },
    ];

    const languages = [
      ["nl-NL", "nl", "en-US", "en"],
      ["en-US", "en", "nl-NL", "nl"],
      ["nl-NL", "nl", "en"],
      ["en-US", "en", "de", "fr"],
    ];

    const timezones = [
      "Europe/Amsterdam",
      "Europe/Brussels",
      "Europe/Berlin",
      "Europe/London",
    ];

    return userAgents.map((userAgent, index) => ({
      userAgent,
      viewport: viewports[index % viewports.length],
      languages: languages[index % languages.length],
      timezone: timezones[index % timezones.length],
      platform: userAgent.includes("Mac") ? "MacIntel" : "Win32",
      hardwareConcurrency: Math.floor(Math.random() * 8) + 4, // 4-12 cores
      deviceMemory: [4, 8, 16][Math.floor(Math.random() * 3)],
      colorDepth: 24,
      pixelDepth: 24,
    }));
  }

  /**
   * Setup stealth browser with comprehensive anti-detection measures
   */
  async setupStealthBrowser(options = {}) {
    try {
      const profile = this.getRandomStealthProfile();

      const browser = await puppeteer.launch({
        headless: options.headless !== false ? "new" : false,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
          "--disable-features=VizDisplayCompositor",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-field-trial-config",
          "--disable-back-forward-cache",
          "--disable-ipc-flooding-protection",
          "--disable-hang-monitor",
          "--disable-prompt-on-repost",
          "--disable-sync",
          "--disable-domain-reliability",
          "--disable-features=TranslateUI",
          "--disable-default-apps",
          "--disable-component-extensions-with-background-pages",
          "--disable-background-networking",
          "--disable-component-update",
          "--disable-client-side-phishing-detection",
          "--disable-datasaver-prompt",
          "--disable-desktop-notifications",
          "--disable-extensions",
          "--disable-web-security",
          "--allow-running-insecure-content",
          "--disable-features=VizDisplayCompositor",
          `--user-agent=${profile.userAgent}`,
          `--window-size=${profile.viewport.width},${profile.viewport.height}`,
          "--lang=nl-NL",
        ],
        ignoreDefaultArgs: ["--enable-automation"],
        ignoreHTTPSErrors: true,
        defaultViewport: null,
      });

      // Store session data
      const sessionId = this.generateSessionId();
      this.sessionData.set(sessionId, {
        browser,
        profile,
        createdAt: new Date(),
        lastActivity: new Date(),
        pageCount: 0,
      });

      logger.info("Stealth browser created", {
        sessionId,
        profile: profile.userAgent,
      });
      return { browser, sessionId, profile };
    } catch (error) {
      logger.error("Failed to setup stealth browser", { error: error.message });
      throw error;
    }
  }

  /**
   * Apply comprehensive stealth measures to a page
   */
  async randomizeFingerprint(page, profile = null) {
    try {
      if (!profile) {
        profile = this.getRandomStealthProfile();
      }

      // Set viewport
      await page.setViewport(profile.viewport);

      // Set user agent
      await page.setUserAgent(profile.userAgent);

      // Set extra HTTP headers
      await page.setExtraHTTPHeaders({
        "Accept-Language": profile.languages.join(","),
        "Accept-Encoding": "gzip, deflate, br",
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Cache-Control": "max-age=0",
        "Sec-Ch-Ua":
          '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": `"${
          profile.platform === "MacIntel" ? "macOS" : "Windows"
        }"`,
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        DNT: "1",
      });

      // Inject stealth scripts
      await page.evaluateOnNewDocument((profileData) => {
        // Override webdriver detection
        Object.defineProperty(navigator, "webdriver", {
          get: () => undefined,
          configurable: true,
        });

        // Override automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;

        // Override navigator properties
        Object.defineProperty(navigator, "languages", {
          get: () => profileData.languages,
          configurable: true,
        });

        Object.defineProperty(navigator, "platform", {
          get: () => profileData.platform,
          configurable: true,
        });

        Object.defineProperty(navigator, "hardwareConcurrency", {
          get: () => profileData.hardwareConcurrency,
          configurable: true,
        });

        if (navigator.deviceMemory !== undefined) {
          Object.defineProperty(navigator, "deviceMemory", {
            get: () => profileData.deviceMemory,
            configurable: true,
          });
        }

        // Override screen properties
        Object.defineProperty(screen, "colorDepth", {
          get: () => profileData.colorDepth,
          configurable: true,
        });

        Object.defineProperty(screen, "pixelDepth", {
          get: () => profileData.pixelDepth,
          configurable: true,
        });

        // Override timezone
        try {
          Intl.DateTimeFormat = class extends Intl.DateTimeFormat {
            constructor(...args) {
              super(...args);
            }

            resolvedOptions() {
              const options = super.resolvedOptions();
              options.timeZone = profileData.timezone;
              return options;
            }
          };
        } catch (e) {
          // Ignore timezone override errors
        }

        // Mock plugins
        Object.defineProperty(navigator, "plugins", {
          get: () => [
            { name: "Chrome PDF Plugin", filename: "internal-pdf-viewer" },
            {
              name: "Chrome PDF Viewer",
              filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
            },
            { name: "Native Client", filename: "internal-nacl-plugin" },
          ],
          configurable: true,
        });

        // Override permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) =>
          parameters.name === "notifications"
            ? Promise.resolve({ state: Notification.permission })
            : originalQuery(parameters);

        // Mock WebGL
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function (parameter) {
          if (parameter === 37445) {
            return "Intel Inc.";
          }
          if (parameter === 37446) {
            return "Intel(R) Iris(TM) Graphics 6100";
          }
          return getParameter.call(this, parameter);
        };
      }, profile);

      logger.debug("Fingerprint randomized", {
        userAgent: profile.userAgent,
        viewport: profile.viewport,
        timezone: profile.timezone,
      });
    } catch (error) {
      logger.error("Failed to randomize fingerprint", { error: error.message });
      throw error;
    }
  }

  /**
   * Simulate human-like interaction patterns
   */
  async simulateHumanBehavior(page, options = {}) {
    try {
      const {
        mouseMovements = true,
        scrolling = true,
        typing = true,
        delays = true,
      } = options;

      if (mouseMovements) {
        await this.simulateMouseMovements(page);
      }

      if (scrolling) {
        await this.simulateScrolling(page);
      }

      if (delays) {
        await this.addRandomDelay(2000, 5000);
      }

      logger.debug("Human behavior simulation completed");
    } catch (error) {
      logger.error("Failed to simulate human behavior", {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Simulate realistic mouse movements
   */
  async simulateMouseMovements(page) {
    try {
      const viewport = await page.viewport();
      const movements = Math.floor(Math.random() * 3) + 2; // 2-4 movements

      for (let i = 0; i < movements; i++) {
        const x = Math.floor(Math.random() * viewport.width);
        const y = Math.floor(Math.random() * viewport.height);

        await page.mouse.move(x, y, {
          steps: Math.floor(Math.random() * 10) + 5,
        });
        await this.addRandomDelay(100, 500);
      }
    } catch (error) {
      logger.error("Failed to simulate mouse movements", {
        error: error.message,
      });
    }
  }

  /**
   * Simulate realistic scrolling behavior
   */
  async simulateScrolling(page) {
    try {
      const scrolls = Math.floor(Math.random() * 3) + 1; // 1-3 scrolls

      for (let i = 0; i < scrolls; i++) {
        const scrollDistance = Math.floor(Math.random() * 500) + 200; // 200-700px

        await page.evaluate((distance) => {
          window.scrollBy(0, distance);
        }, scrollDistance);

        await this.addRandomDelay(500, 1500);
      }
    } catch (error) {
      logger.error("Failed to simulate scrolling", { error: error.message });
    }
  }

  /**
   * Simulate human-like typing with realistic delays
   */
  async simulateTyping(page, selector, text, options = {}) {
    try {
      const { minDelay = 10, maxDelay = 40, mistakes = true } = options;

      await page.focus(selector);
      await this.addRandomDelay(200, 500);

      for (let i = 0; i < text.length; i++) {
        const char = text[i];

        // Simulate occasional typing mistakes
        if (mistakes && Math.random() < 0.02) {
          // 2% chance of mistake
          const wrongChar = String.fromCharCode(char.charCodeAt(0) + 1);
          await page.keyboard.type(wrongChar);
          await this.addRandomDelay(100, 300);
          await page.keyboard.press("Backspace");
          await this.addRandomDelay(100, 200);
        }

        await page.keyboard.type(char);
        await this.addRandomDelay(minDelay, maxDelay);
      }
    } catch (error) {
      logger.error("Failed to simulate typing", {
        error: error.message,
        selector,
      });
      throw error;
    }
  }

  /**
   * Detect CAPTCHA or verification pages
   */
  async handleCaptcha(page) {
    try {
      const content = await page.content();
      const url = page.url();

      // Check for CAPTCHA indicators
      const hasCaptcha = this.captchaDetectionPatterns.some((pattern) =>
        content.toLowerCase().includes(pattern.toLowerCase())
      );

      if (hasCaptcha) {
        logger.warn("CAPTCHA or verification page detected", { url });

        // Take screenshot for debugging
        const screenshot = await page.screenshot({
          fullPage: true,
          type: "png",
        });

        return {
          detected: true,
          type: this.identifyCaptchaType(content),
          url,
          screenshot: screenshot.toString("base64"),
          timestamp: new Date(),
        };
      }

      return { detected: false };
    } catch (error) {
      logger.error("Failed to handle CAPTCHA detection", {
        error: error.message,
      });
      return { detected: false, error: error.message };
    }
  }

  /**
   * Identify the type of CAPTCHA or verification system
   */
  identifyCaptchaType(content) {
    const lowerContent = content.toLowerCase();

    if (lowerContent.includes("recaptcha")) return "reCAPTCHA";
    if (lowerContent.includes("hcaptcha")) return "hCaptcha";
    if (lowerContent.includes("cloudflare")) return "Cloudflare";
    if (
      lowerContent.includes("funda") ||
      lowerContent.includes("je bent bijna op de pagina") ||
      lowerContent.includes("we houden ons platform graag veilig")
    ) {
      return "Funda Verification";
    }

    return "Unknown";
  }

  /**
   * Detect if the page indicates blocking or rate limiting
   */
  async detectBlocking(page) {
    try {
      const content = await page.content();
      const url = page.url();
      const status = page.response?.status();

      const blockingIndicators = [
        "blocked",
        "rate limit",
        "too many requests",
        "429",
        "access denied",
        "forbidden",
        "temporarily unavailable",
        "service unavailable",
      ];

      const isBlocked =
        blockingIndicators.some((indicator) =>
          content.toLowerCase().includes(indicator.toLowerCase())
        ) ||
        status === 429 ||
        status === 403;

      if (isBlocked) {
        logger.warn("Blocking detected", { url, status });
        return {
          blocked: true,
          status,
          url,
          timestamp: new Date(),
        };
      }

      return { blocked: false };
    } catch (error) {
      logger.error("Failed to detect blocking", { error: error.message });
      return { blocked: false, error: error.message };
    }
  }

  /**
   * Manage browser sessions and cleanup
   */
  async cleanupSession(sessionId) {
    try {
      const session = this.sessionData.get(sessionId);

      if (session) {
        await session.browser.close();
        this.sessionData.delete(sessionId);
        logger.info("Session cleaned up", { sessionId });
      }
    } catch (error) {
      logger.error("Failed to cleanup session", {
        sessionId,
        error: error.message,
      });
    }
  }

  /**
   * Cleanup all active sessions
   */
  async cleanupAllSessions() {
    try {
      const sessionIds = Array.from(this.sessionData.keys());

      await Promise.all(
        sessionIds.map((sessionId) => this.cleanupSession(sessionId))
      );

      logger.info("All sessions cleaned up", { count: sessionIds.length });
    } catch (error) {
      logger.error("Failed to cleanup all sessions", { error: error.message });
    }
  }

  /**
   * Adapt to new anti-automation measures
   */
  async adaptToChanges(detectionData) {
    try {
      logger.info("Adapting to new detection measures", { detectionData });

      // Implement adaptive strategies based on detection data
      if (detectionData.type === "fingerprint") {
        // Generate new stealth profiles
        this.stealthProfiles = this.generateStealthProfiles();
      }

      if (detectionData.type === "behavior") {
        // Adjust behavior simulation parameters
        // This could be expanded with machine learning
      }

      if (detectionData.type === "rate_limit") {
        // Implement longer delays
        return { adapted: true, recommendedDelay: 60000 }; // 1 minute
      }

      return { adapted: true };
    } catch (error) {
      logger.error("Failed to adapt to changes", { error: error.message });
      return { adapted: false, error: error.message };
    }
  }

  /**
   * Utility methods
   */
  getRandomStealthProfile() {
    return this.stealthProfiles[
      Math.floor(Math.random() * this.stealthProfiles.length)
    ];
  }

  generateSessionId() {
    const timestamp = Date.now();
    const random1 = Math.random().toString(36).substr(2, 9);
    const random2 = Math.random().toString(36).substr(2, 5);
    const counter = Math.floor(Math.random() * 100000);
    const nanoTime = process.hrtime.bigint();
    return `session_${timestamp}_${random1}_${random2}_${counter}_${nanoTime.toString(
      36
    )}`;
  }

  async addRandomDelay(min = 1000, max = 3000) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    const sessions = Array.from(this.sessionData.values());

    return {
      activeSessions: sessions.length,
      totalPageViews: sessions.reduce(
        (sum, session) => sum + session.pageCount,
        0
      ),
      oldestSession:
        sessions.length > 0
          ? Math.min(...sessions.map((s) => s.createdAt.getTime()))
          : null,
      profiles: this.stealthProfiles.length,
    };
  }
}

module.exports = AntiDetectionSystem;
