const AutoApplicationSettings = require("../models/AutoApplicationSettings");
const ApplicationQueue = require("../models/ApplicationQueue");
const Listing = require("../models/Listing");
// Using lazy loading to avoid circular dependency
// const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const autoApplicationService = require("./autoApplicationService");
const { loggers } = require("./logger");

/**
 * ScraperAutoApplicationIntegration - Integrates scraper with auto-application system
 *
 * This service provides:
 * - Real-time listing processing for immediate application opportunities
 * - Listing quality scoring to prioritize high-value applications
 * - Integration with existing listing database and caching system
 * - Duplicate detection to avoid multiple applications to same property
 * - Triggers auto-application checks when new listings are found
 */
class ScraperAutoApplicationIntegration {
  constructor() {
    this.processedListings = new Set(); // Cache to track processed listings
    this.qualityScoreCache = new Map(); // Cache for quality scores
    this.CACHE_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours
    this.MIN_QUALITY_SCORE = 0.6; // Minimum score to trigger auto-application
    this.MAX_CONCURRENT_PROCESSING = 5; // Limit concurrent processing
    this.currentlyProcessing = new Set(); // Track currently processing listings
  }

  /**
   * Get AutoApplicationSettings model with lazy loading to avoid circular dependency
   * @private
   */
  _getAutoApplicationSettings() {
    if (!this._AutoApplicationSettingsModel) {
      this._AutoApplicationSettingsModel = require("../models/AutoApplicationSettings");
    }
    return this._AutoApplicationSettingsModel;
  }

  /**
   * Process new listings from scraper and trigger auto-application checks
   * @param {Array} listings - Array of new listings from scraper
   * @param {string} source - Source of listings (e.g., 'funda.nl')
   * @returns {Promise<Object>} Processing results
   */
  async processNewListings(listings, source = "unknown") {
    try {
      loggers.app.info(
        `Processing ${listings.length} new listings from ${source}`
      );

      const results = {
        processed: 0,
        duplicates: 0,
        autoApplicationTriggered: 0,
        errors: 0,
        qualityScores: [],
      };

      // Get all users with auto-application enabled
      const AutoApplicationSettings = this._getAutoApplicationSettings();
      const enabledUsers = await AutoApplicationSettings.find({
        enabled: true,
      });
      if (enabledUsers.length === 0) {
        loggers.app.info(
          "No users have auto-application enabled, skipping processing"
        );
        return results;
      }

      loggers.app.info(
        `Found ${enabledUsers.length} users with auto-application enabled`
      );

      // Process listings in batches to avoid overwhelming the system
      const batchSize = Math.min(
        this.MAX_CONCURRENT_PROCESSING,
        listings.length
      );
      for (let i = 0; i < listings.length; i += batchSize) {
        const batch = listings.slice(i, i + batchSize);
        const batchPromises = batch.map((listing) =>
          this._processListing(listing, source, enabledUsers)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Aggregate results
        batchResults.forEach((result, index) => {
          if (result.status === "fulfilled") {
            const listingResult = result.value;
            results.processed++;
            if (listingResult.isDuplicate) results.duplicates++;
            if (listingResult.autoApplicationTriggered)
              results.autoApplicationTriggered++;
            if (listingResult.qualityScore)
              results.qualityScores.push(listingResult.qualityScore);
          } else {
            results.errors++;
            loggers.app.error(`Error processing listing: ${result.reason}`);
          }
        });

        // Add delay between batches to avoid overwhelming the system
        if (i + batchSize < listings.length) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      loggers.app.info(
        `Listing processing complete: ${JSON.stringify(results)}`
      );
      return results;
    } catch (error) {
      loggers.app.error("Error processing new listings:", error);
      throw error;
    }
  }

  /**
   * Process a single listing
   * @private
   */
  async _processListing(listing, source, enabledUsers) {
    const listingId = this._generateListingId(listing);

    try {
      // Check if already processing this listing
      if (this.currentlyProcessing.has(listingId)) {
        return { isDuplicate: true, autoApplicationTriggered: false };
      }

      this.currentlyProcessing.add(listingId);

      // Check for duplicates in database
      const existingListing = await this._checkForDuplicate(listing);
      if (existingListing) {
        loggers.app.debug(`Duplicate listing found: ${listing.title}`);
        return { isDuplicate: true, autoApplicationTriggered: false };
      }

      // Calculate quality score
      const qualityScore = await this._calculateQualityScore(listing);

      // Skip low-quality listings
      if (qualityScore < this.MIN_QUALITY_SCORE) {
        loggers.app.debug(
          `Low quality listing skipped: ${listing.title} (score: ${qualityScore})`
        );
        return {
          isDuplicate: false,
          autoApplicationTriggered: false,
          qualityScore,
        };
      }

      // Find matching users and trigger auto-applications
      let autoApplicationTriggered = false;
      for (const userSettings of enabledUsers) {
        if (await this._matchesUserCriteria(listing, userSettings)) {
          await this._triggerAutoApplication(
            listing,
            userSettings,
            qualityScore
          );
          autoApplicationTriggered = true;
        }
      }

      // Mark as processed
      this.processedListings.add(listingId);

      return {
        isDuplicate: false,
        autoApplicationTriggered,
        qualityScore,
      };
    } catch (error) {
      loggers.app.error(`Error processing listing ${listing.title}:`, error);
      throw error;
    } finally {
      this.currentlyProcessing.delete(listingId);
    }
  }

  /**
   * Calculate quality score for a listing
   * @private
   */
  async _calculateQualityScore(listing) {
    try {
      const listingId = this._generateListingId(listing);

      // Check cache first
      if (this.qualityScoreCache.has(listingId)) {
        const cached = this.qualityScoreCache.get(listingId);
        if (Date.now() - cached.timestamp < this.CACHE_EXPIRY_MS) {
          return cached.score;
        }
      }

      let score = 0.3; // Base score (lower to ensure low quality listings score lower)

      // Price availability and format (+0.2)
      if (
        listing.price &&
        listing.price !== "Prijs op aanvraag" &&
        listing.price.includes("€")
      ) {
        score += 0.2;
      }

      // Description quality (+0.15)
      if (listing.description && listing.description.length > 100) {
        score += 0.15;
      }

      // Property details completeness (+0.2)
      const detailFields = [
        "size",
        "bedrooms",
        "rooms",
        "year",
        "propertyType",
      ];
      const completedFields = detailFields.filter(
        (field) => listing[field] && listing[field] !== null
      );
      score += (completedFields.length / detailFields.length) * 0.2;

      // Images availability (+0.1)
      if (listing.images && listing.images.length > 0) {
        score += 0.1;
      }

      // Location specificity (+0.05)
      if (listing.location && listing.location.length > 5) {
        score += 0.05;
      }

      // Recency bonus (newer listings get higher scores)
      if (listing.dateAdded) {
        const hoursOld =
          (Date.now() - new Date(listing.dateAdded).getTime()) /
          (1000 * 60 * 60);
        if (hoursOld < 1) score += 0.1; // Very new
        else if (hoursOld < 6) score += 0.05; // Recent
      }

      // Property type preference (apartments and houses score higher)
      if (listing.propertyType) {
        const type = listing.propertyType.toLowerCase();
        if (type.includes("appartement") || type.includes("huis")) {
          score += 0.05;
        }
      }

      // Cap score at 1.0
      score = Math.min(score, 1.0);

      // Cache the score
      this.qualityScoreCache.set(listingId, {
        score,
        timestamp: Date.now(),
      });

      return score;
    } catch (error) {
      loggers.app.error("Error calculating quality score:", error);
      return 0.5; // Return neutral score on error
    }
  }

  /**
   * Check if listing matches user criteria
   * @private
   */
  async _matchesUserCriteria(listing, userSettings) {
    try {
      const criteria = userSettings.criteria;
      if (!criteria) return false;

      // Price check
      if (criteria.maxPrice && listing.price) {
        const price = this._extractPriceNumber(listing.price);
        if (price && price > criteria.maxPrice) {
          loggers.app.debug(
            `Listing price ${price} exceeds max price ${criteria.maxPrice}`
          );
          return false;
        }
      }

      // Room count checks
      if (criteria.minRooms && listing.rooms) {
        const rooms = parseInt(listing.rooms);
        if (rooms && rooms < criteria.minRooms) {
          return false;
        }
      }

      if (criteria.maxRooms && listing.rooms) {
        const rooms = parseInt(listing.rooms);
        if (rooms && rooms > criteria.maxRooms) {
          return false;
        }
      }

      // Property type check with Dutch term mapping
      if (criteria.propertyTypes && criteria.propertyTypes.length > 0) {
        const listingType = listing.propertyType?.toLowerCase() || "";
        
        const matchesType = criteria.propertyTypes.some((type) => {
          const criteriaTypeLower = type.toLowerCase();
          
          // Direct match
          if (listingType.includes(criteriaTypeLower)) {
            return true;
          }
          
          // Dutch to English mappings
          const dutchMappings = {
            'woning': ['house', 'apartment'], // Generic dwelling - matches both house and apartment
            'appartement': ['apartment'],
            'huis': ['house'],
            'eengezinswoning': ['house'],
            'studio': ['studio'],
            'kamer': ['room'],
            'ruimte': ['room']
          };
          
          // Check if listing type maps to criteria type
          for (const [dutchTerm, englishTypes] of Object.entries(dutchMappings)) {
            if (listingType.includes(dutchTerm) && englishTypes.includes(criteriaTypeLower)) {
              return true;
            }
          }
          
          return false;
        });
        
        if (!matchesType) {
          loggers.app.debug(
            `Property type mismatch: "${listing.propertyType}" does not match ${JSON.stringify(criteria.propertyTypes)}`
          );
          return false;
        }
      }

      // Location check
      if (criteria.locations && criteria.locations.length > 0) {
        const listingLocation = listing.location?.toLowerCase() || "";
        const matchesLocation = criteria.locations.some((location) =>
          listingLocation.includes(location.toLowerCase())
        );
        if (!matchesLocation) {
          return false;
        }
      }

      // Size checks
      if (criteria.minSize && listing.size) {
        const size = parseInt(listing.size);
        if (size && size < criteria.minSize) {
          return false;
        }
      }

      if (criteria.maxSize && listing.size) {
        const size = parseInt(listing.size);
        if (size && size > criteria.maxSize) {
          return false;
        }
      }

      // Keyword exclusion check
      if (criteria.excludeKeywords && criteria.excludeKeywords.length > 0) {
        const searchText =
          `${listing.title} ${listing.description}`.toLowerCase();
        const hasExcludedKeyword = criteria.excludeKeywords.some((keyword) =>
          searchText.includes(keyword.toLowerCase())
        );
        if (hasExcludedKeyword) {
          return false;
        }
      }

      // Keyword inclusion check
      if (criteria.includeKeywords && criteria.includeKeywords.length > 0) {
        const searchText =
          `${listing.title} ${listing.description}`.toLowerCase();
        const hasIncludedKeyword = criteria.includeKeywords.some((keyword) =>
          searchText.includes(keyword.toLowerCase())
        );
        if (!hasIncludedKeyword) {
          return false;
        }
      }

      // Furnished preference check
      if (criteria.furnished !== undefined && listing.interior) {
        const isFurnished = listing.interior
          .toLowerCase()
          .includes("gemeubileerd");
        if (criteria.furnished !== isFurnished) {
          return false;
        }
      }

      return true;
    } catch (error) {
      loggers.app.error("Error matching user criteria:", error);
      return false;
    }
  }

  /**
   * Trigger auto-application for a matching listing
   * @private
   */
  async _triggerAutoApplication(listing, userSettings, qualityScore) {
    try {
      loggers.app.info(
        `Triggering auto-application for user ${userSettings.userId} on listing: ${listing.title}`
      );

      // Check if user has already applied to this listing
      const existingApplication = await ApplicationQueue.findOne({
        userId: userSettings.userId,
        listingUrl: listing.url,
      });

      if (existingApplication) {
        loggers.app.debug(
          `User ${userSettings.userId} already has application for listing: ${listing.url}`
        );
        return;
      }

      // Check daily application limit
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayApplications = await ApplicationQueue.countDocuments({
        userId: userSettings.userId,
        createdAt: { $gte: today },
      });

      if (todayApplications >= userSettings.settings.maxApplicationsPerDay) {
        loggers.app.info(
          `Daily application limit reached for user ${userSettings.userId}`
        );
        return;
      }

      // Save listing to database if not already saved
      const savedListing = await this._ensureListingSaved(listing);

      // Process the new listing through auto-application service
      await autoApplicationService.processNewListing(
        savedListing, // Use the saved listing with MongoDB _id
        userSettings.userId,
        {
          qualityScore,
          source: "scraper-integration",
          priority: qualityScore > 0.8 ? "high" : "normal",
        }
      );

      loggers.app.info(
        `Auto-application triggered successfully for user ${userSettings.userId}`
      );
    } catch (error) {
      loggers.app.error("Error triggering auto-application:", error);
      throw error;
    }
  }

  /**
   * Check for duplicate listings in database
   * @private
   */
  async _checkForDuplicate(listing) {
    try {
      // Check by URL (most reliable)
      if (listing.url) {
        const existingByUrl = await Listing.findOne({ url: listing.url });
        if (existingByUrl) return existingByUrl;
      }

      // Check by title and location (fallback)
      if (listing.title && listing.location) {
        const existingByTitleLocation = await Listing.findOne({
          title: listing.title,
          location: listing.location,
        });
        if (existingByTitleLocation) return existingByTitleLocation;
      }

      return null;
    } catch (error) {
      loggers.app.error("Error checking for duplicates:", error);
      return null;
    }
  }

  /**
   * Ensure listing is saved to database
   * @private
   */
  async _ensureListingSaved(listing) {
    try {
      const existingListing = await this._checkForDuplicate(listing);
      if (existingListing) {
        return existingListing;
      }

      // Create new listing
      const newListing = new Listing({
        title: listing.title,
        price: listing.price || "Prijs op aanvraag",
        location: listing.location,
        url: listing.url,
        size: listing.size,
        bedrooms: listing.bedrooms,
        rooms: listing.rooms,
        propertyType: listing.propertyType,
        description: listing.description,
        year: listing.year,
        interior: listing.interior,
        source: listing.source,
        images: listing.images || [],
        dateAdded: listing.dateAdded || new Date(),
      });

      await newListing.save();
      loggers.app.debug(`Saved new listing to database: ${listing.title}`);
      return newListing;
    } catch (error) {
      loggers.app.error("Error saving listing to database:", error);
      throw error;
    }
  }

  /**
   * Generate unique ID for listing
   * @private
   */
  _generateListingId(listing) {
    if (listing.url) {
      return listing.url;
    }
    return `${listing.title}-${listing.location}-${listing.price}`
      .replace(/\s+/g, "-")
      .toLowerCase();
  }

  /**
   * Extract numeric price from price string
   * @private
   */
  _extractPriceNumber(priceString) {
    try {
      if (!priceString || typeof priceString !== "string") return null;

      // Remove currency symbols and text, extract numbers
      const match = priceString.match(/[\d.,]+/);
      if (!match) return null;

      // Handle European number format (1.500,50 or 1,500.50)
      let numberStr = match[0];

      // If contains both comma and dot, determine which is decimal separator
      if (numberStr.includes(",") && numberStr.includes(".")) {
        // If comma comes after dot, comma is decimal separator
        if (numberStr.lastIndexOf(",") > numberStr.lastIndexOf(".")) {
          numberStr = numberStr.replace(/\./g, "").replace(",", ".");
        } else {
          // Dot is decimal separator, comma is thousands separator
          numberStr = numberStr.replace(/,/g, "");
        }
      } else if (numberStr.includes(",")) {
        // Only comma - could be thousands separator or decimal
        const parts = numberStr.split(",");
        if (parts.length === 2 && parts[1].length <= 2) {
          // Likely decimal separator
          numberStr = numberStr.replace(",", ".");
        } else {
          // Likely thousands separator
          numberStr = numberStr.replace(/,/g, "");
        }
      } else if (numberStr.includes(".")) {
        // Only dot - could be thousands separator or decimal
        const parts = numberStr.split(".");
        if (parts.length === 2 && parts[1].length <= 2) {
          // Likely decimal separator - keep as is
        } else {
          // Likely thousands separator - remove dots
          numberStr = numberStr.replace(/\./g, "");
        }
      }

      const price = parseFloat(numberStr);
      return isNaN(price) ? null : price;
    } catch (error) {
      loggers.app.error("Error extracting price number:", error);
      return null;
    }
  }

  /**
   * Clear caches (for maintenance)
   */
  clearCaches() {
    this.processedListings.clear();
    this.qualityScoreCache.clear();
    loggers.app.info("Scraper integration caches cleared");
  }

  /**
   * Get integration statistics
   */
  getStatistics() {
    return {
      processedListingsCount: this.processedListings.size,
      qualityScoreCacheSize: this.qualityScoreCache.size,
      currentlyProcessingCount: this.currentlyProcessing.size,
      cacheExpiryMs: this.CACHE_EXPIRY_MS,
      minQualityScore: this.MIN_QUALITY_SCORE,
    };
  }
}

module.exports = new ScraperAutoApplicationIntegration();
