const AutoApplicationSettings = require("../models/AutoApplicationSettings");
const ApplicationQueue = require("../models/ApplicationQueue");
const ApplicationResult = require("../models/ApplicationResult");
const User = require("../models/User");
const Listing = require("../models/Listing");
const aiService = require("./aiService");
const websocketService = require("./websocketService");
const documentVaultService = require("./documentVaultService");
const ErrorHandlingService = require("./errorHandlingService");
const { loggers } = require("./logger");

/**
 * AutoApplicationService - Core service for automated property applications
 *
 * This service provides:
 * - Auto-application enabling/disabling with validation
 * - User settings management with persistence
 * - New listing processing and criteria matching
 * - Integration with AI service for application content generation
 * - Application queue management and status tracking
 */
class AutoApplicationService {
  constructor() {
    // Enable console logging for auto-application debugging
    process.env.AUTO_APPLICATION_CONSOLE_LOGS = "true";

    this.isProcessing = false;
    this.processingInterval = null;
    this.PROCESSING_INTERVAL_MS = 2 * 60 * 1000; // 2 minutes
    this.MAX_DAILY_APPLICATIONS = 20;
    this.MIN_APPLICATION_DELAY_MS = 2 * 60 * 1000; // 2 minutes
    this.MAX_APPLICATION_DELAY_MS = 10 * 60 * 1000; // 10 minutes
    this.errorHandlingService = new ErrorHandlingService();
  }

  /**
   * Get AutoApplicationSettings model with lazy loading to avoid circular dependency
   * @private
   */
  _getAutoApplicationSettings() {
    if (!this._AutoApplicationSettingsModel) {
      this._AutoApplicationSettingsModel = require("../models/AutoApplicationSettings");
    }
    return this._AutoApplicationSettingsModel;
  }

  /**
   * Enable auto-application for a user
   * @param {string} userId - User ID
   * @param {Object} settings - Auto-application settings
   * @returns {Promise<Object>} Updated settings
   */
  async enableAutoApplication(userId, settings) {
    try {
      loggers.app.info(`Enabling auto-application for user ${userId}`);

      // Validate user exists
      const user = await User.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Validate required settings
      this._validateSettings(settings);

      // Find or create auto-application settings
      const AutoApplicationSettings = this._getAutoApplicationSettings();
      let autoSettings = await AutoApplicationSettings.findByUserId(userId);

      if (!autoSettings) {
        autoSettings = new AutoApplicationSettings({
          userId,
          enabled: false,
          settings: {},
          criteria: {},
          personalInfo: {},
          documents: [],
          statistics: {},
          status: {},
        });
      }

      // Update settings
      autoSettings.enabled = true;
      autoSettings.settings = {
        maxApplicationsPerDay: Math.min(
          settings.maxApplicationsPerDay || 5,
          this.MAX_DAILY_APPLICATIONS
        ),
        applicationTemplate: settings.applicationTemplate || "professional",
        autoSubmit: settings.autoSubmit !== false, // Default to true
        requireManualReview: settings.requireManualReview || false,
        notificationPreferences: {
          immediate: settings.notificationPreferences?.immediate !== false,
          daily: settings.notificationPreferences?.daily !== false,
          weekly: settings.notificationPreferences?.weekly || false,
        },
        language: settings.language || "english",
      };

      // Update criteria
      if (settings.criteria) {
        autoSettings.criteria = {
          maxPrice: settings.criteria.maxPrice,
          minRooms: settings.criteria.minRooms || 1,
          maxRooms: settings.criteria.maxRooms || 10,
          propertyTypes: settings.criteria.propertyTypes || [],
          locations: settings.criteria.locations || [],
          excludeKeywords: settings.criteria.excludeKeywords || [],
          includeKeywords: settings.criteria.includeKeywords || [],
          minSize: settings.criteria.minSize,
          maxSize: settings.criteria.maxSize,
          furnished: settings.criteria.furnished,
          petsAllowed: settings.criteria.petsAllowed,
          smokingAllowed: settings.criteria.smokingAllowed,
        };
      }

      // Update personal info if provided
      if (settings.personalInfo) {
        autoSettings.personalInfo = {
          ...autoSettings.personalInfo,
          ...settings.personalInfo,
        };
      }

      // Reset error count when enabling
      if (!autoSettings.status) {
        autoSettings.status = {};
      }
      autoSettings.status.errorCount = 0;
      autoSettings.status.lastError = undefined;
      autoSettings.status.pausedReason = undefined;
      autoSettings.status.pausedUntil = undefined;

      await autoSettings.save();

      // Start processing if not already running
      this._startProcessing();

      loggers.app.info(`Auto-application enabled for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);
    } catch (error) {
      const recoveryResult = await this.errorHandlingService.handleError(
        error,
        {
          service: "AutoApplicationService",
          method: "enableAutoApplication",
          userId,
          operation: "enable_auto_application",
        }
      );

      loggers.app.error(
        `Error enabling auto-application for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Disable auto-application for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated settings
   */
  async disableAutoApplication(userId) {
    try {
      loggers.app.info(`Disabling auto-application for user ${userId}`);

      const AutoApplicationSettings = this._getAutoApplicationSettings();
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        throw new Error("Auto-application settings not found");
      }

      autoSettings.enabled = false;
      autoSettings.status.isActive = false;
      autoSettings.status.pausedReason = "Disabled by user";

      await autoSettings.save();

      // Cancel any pending applications for this user
      await ApplicationQueue.updateMany(
        { userId, status: "pending" },
        { status: "cancelled", processedAt: new Date() }
      );

      loggers.app.info(`Auto-application disabled for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);
    } catch (error) {
      loggers.app.error(
        `Error disabling auto-application for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Update user auto-application settings
   * @param {string} userId - User ID
   * @param {Object} updates - Settings updates
   * @returns {Promise<Object>} Updated settings
   */
  async updateUserSettings(userId, updates) {
    try {
      loggers.app.info(`Updating auto-application settings for user ${userId}`);

      const AutoApplicationSettings = this._getAutoApplicationSettings();
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        throw new Error("Auto-application settings not found");
      }

      // Validate updates
      if (updates.settings) {
        this._validateSettingsUpdate(updates.settings);
      }

      // Update settings
      if (updates.settings) {
        autoSettings.settings = {
          ...autoSettings.settings,
          ...updates.settings,
          maxApplicationsPerDay: Math.min(
            updates.settings.maxApplicationsPerDay ||
              autoSettings.settings.maxApplicationsPerDay,
            this.MAX_DAILY_APPLICATIONS
          ),
        };
      }

      // Update criteria
      if (updates.criteria) {
        autoSettings.criteria = {
          ...autoSettings.criteria,
          ...updates.criteria,
        };
      }

      // Update personal info
      if (updates.personalInfo) {
        autoSettings.personalInfo = {
          ...autoSettings.personalInfo,
          ...updates.personalInfo,
        };
      }

      await autoSettings.save();

      loggers.app.info(`Auto-application settings updated for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);
    } catch (error) {
      loggers.app.error(
        `Error updating auto-application settings for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get user auto-application settings
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User settings
   */
  async getUserSettings(userId) {
    try {
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        return null;
      }

      return this._formatSettingsResponse(autoSettings);
    } catch (error) {
      loggers.app.error(
        `Error getting auto-application settings for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get user auto-application settings (alias for getUserSettings)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User settings
   */
  async getSettings(userId) {
    return this.getUserSettings(userId);
  }

  /**
   * Update user auto-application settings (alias for updateUserSettings)
   * @param {string} userId - User ID
   * @param {Object} updates - Settings updates
   * @returns {Promise<Object>} Updated settings
   */
  async updateSettings(userId, updates) {
    return this.updateUserSettings(userId, updates);
  }

  /**
   * Process existing listings in the database for auto-application
   * @param {string} userId - Optional user ID to process only for specific user
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processing results
   */
  async processExistingListings(userId = null, options = {}) {
    try {
      const {
        limit = 100,
        daysBack = 30,
        skipRecentlyProcessed = true,
        minQualityScore = 0.6,
      } = options;

      loggers.app.info(
        `Processing existing listings for auto-application${userId ? ` for user ${userId}` : ''}`
      );

      // Get all active auto-application users (or specific user)
      let activeUsersQuery = {
        enabled: true,
        'status.isActive': true,
        $or: [
          { 'status.pausedUntil': { $exists: false } },
          { 'status.pausedUntil': { $lt: new Date() } },
        ],
      };
      
      if (userId) {
        activeUsersQuery.userId = userId;
      }
      
      const activeUsers = await AutoApplicationSettings.find(activeUsersQuery);
      
      if (activeUsers.length === 0) {
        return {
          success: true,
          message: 'No active users with auto-application enabled',
          processed: 0,
          applicationsCreated: 0,
          usersProcessed: 0,
        };
      }

      loggers.app.info(`Found ${activeUsers.length} active auto-application users`);

      // Get existing listings from the database
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysBack);
      
      const listingsQuery = {
        dateAdded: { $gte: cutoffDate },
      };
      
      // Optionally skip listings that were recently processed
      if (skipRecentlyProcessed) {
        const recentlyProcessedCutoff = new Date();
        recentlyProcessedCutoff.setHours(recentlyProcessedCutoff.getHours() - 6); // Skip if processed in last 6 hours
        
        // Get listing IDs that were recently processed
        const recentApplications = await ApplicationQueue.distinct('listingId', {
          createdAt: { $gte: recentlyProcessedCutoff },
        });
        
        if (recentApplications.length > 0) {
          listingsQuery._id = { $nin: recentApplications };
        }
      }
      
      const listings = await Listing.find(listingsQuery)
        .sort({ dateAdded: -1 })
        .limit(limit);

      if (listings.length === 0) {
        return {
          success: true,
          message: 'No eligible listings found to process',
          processed: 0,
          applicationsCreated: 0,
          usersProcessed: activeUsers.length,
        };
      }

      loggers.app.info(`Found ${listings.length} listings to process`);

      let totalProcessed = 0;
      let totalApplicationsCreated = 0;
      let usersProcessed = 0;
      const results = [];

      // Process each user against all eligible listings
      for (const userSettings of activeUsers) {
        try {
          loggers.app.debug(`Processing listings for user ${userSettings.userId}`);
          
          // Check user's daily limit
          if (
            userSettings.statistics.dailyApplicationCount >=
            userSettings.settings.maxApplicationsPerDay
          ) {
            loggers.app.debug(
              `User ${userSettings.userId} has reached daily application limit`
            );
            continue;
          }
          
          let userApplicationsCreated = 0;
          const remainingApplications = 
            userSettings.settings.maxApplicationsPerDay - 
            userSettings.statistics.dailyApplicationCount;

          // Process listings for this user
          for (const listing of listings) {
            try {
              // Stop if user has reached daily limit
              if (userApplicationsCreated >= remainingApplications) {
                loggers.app.debug(
                  `User ${userSettings.userId} reached daily limit during processing`
                );
                break;
              }

              // Check if listing matches user criteria
              const matchesCriteria = userSettings.matchesCriteria(listing);
              if (!matchesCriteria) {
                continue;
              }

              // Check if user already applied to this listing
              const existingApplication = await ApplicationQueue.findOne({
                userId: userSettings.userId,
                listingId: listing._id,
              });

              if (existingApplication) {
                continue;
              }

              // Calculate quality score (simplified version)
              let qualityScore = 0.5;
              if (listing.price && listing.price !== 'Prijs op aanvraag') {
                qualityScore += 0.2;
              }
              if (listing.description && listing.description.length > 100) {
                qualityScore += 0.2;
              }
              if (listing.size || listing.bedrooms || listing.rooms) {
                qualityScore += 0.1;
              }
              
              // Skip low-quality listings
              if (qualityScore < minQualityScore) {
                continue;
              }

              // Generate application content
              const user = await User.findById(userSettings.userId);
              if (!user) {
                loggers.app.error(`User ${userSettings.userId} not found`);
                continue;
              }

              const applicationContent = await this._generateApplicationContent(
                listing,
                user,
                userSettings
              );

              // Create queue item
              const priority = this._calculatePriority(listing, userSettings);
              const scheduledAt = this._calculateScheduledTime();

              const queueItem = new ApplicationQueue({
                userId: userSettings.userId,
                listingId: listing._id,
                listingUrl: listing.url,
                priority,
                status: 'pending',
                attempts: 0,
                maxAttempts: 3,
                scheduledAt,
                applicationData: {
                  personalInfo: userSettings.personalInfo,
                  documents: userSettings.documents.filter(
                    (doc) => doc.uploaded && doc.verified
                  ),
                  preferences: userSettings.settings,
                },
                generatedContent: applicationContent,
                metadata: {
                  formType: 'unknown',
                  detectionMethods: [],
                  processingTime: 0,
                  source: 'existing-listings-batch',
                  qualityScore,
                },
              });

              await queueItem.save();
              userApplicationsCreated++;
              totalApplicationsCreated++;

              loggers.app.debug(
                `Created application for user ${userSettings.userId}, listing ${listing._id}`
              );
            } catch (listingError) {
              loggers.app.error(
                `Error processing listing ${listing._id} for user ${userSettings.userId}:`,
                listingError
              );
            }
          }

          if (userApplicationsCreated > 0) {
            results.push({
              userId: userSettings.userId,
              applicationsCreated: userApplicationsCreated,
            });
          }
          
          usersProcessed++;
          totalProcessed += listings.length;
          
          loggers.app.info(
            `User ${userSettings.userId}: created ${userApplicationsCreated} applications`
          );
        } catch (userError) {
          loggers.app.error(
            `Error processing user ${userSettings.userId}:`,
            userError
          );
        }
      }

      loggers.app.info(
        `Existing listings processing complete: ${totalApplicationsCreated} applications created for ${usersProcessed} users`
      );

      return {
        success: true,
        processed: totalProcessed,
        applicationsCreated: totalApplicationsCreated,
        usersProcessed,
        listingsFound: listings.length,
        userResults: results,
      };
    } catch (error) {
      loggers.app.error('Error processing existing listings:', error);
      throw error;
    }
  }

  /**
   * Process a new listing for auto-application
   * @param {Object} listing - New listing data
   * @returns {Promise<Array>} Array of created applications
   */
  async processNewListing(listing) {
    try {
      loggers.app.info(
        `Processing new listing for auto-application: ${listing._id}`
      );
      loggers.app.debug("Listing details for processing", {
        listingId: listing._id,
        title: listing.title,
        location: listing.location,
        price: listing.price,
        propertyType: listing.propertyType,
        dateAdded: listing.dateAdded,
      });

      // Get all active auto-application users
      const activeUsers = await AutoApplicationSettings.findActiveUsers();
      loggers.app.debug("Active auto-application users found", {
        totalActiveUsers: activeUsers.length,
        listingId: listing._id,
      });

      const createdApplications = [];

      for (const userSettings of activeUsers) {
        try {
          loggers.app.debug("Processing user for auto-application", {
            userId: userSettings.userId,
            listingId: listing._id,
            userDailyCount: userSettings.statistics.dailyApplicationCount,
            userDailyLimit: userSettings.settings.maxApplicationsPerDay,
          });

          // Check if user has reached daily limit
          if (
            userSettings.statistics.dailyApplicationCount >=
            userSettings.settings.maxApplicationsPerDay
          ) {
            loggers.app.debug("User reached daily application limit", {
              userId: userSettings.userId,
              dailyCount: userSettings.statistics.dailyApplicationCount,
              dailyLimit: userSettings.settings.maxApplicationsPerDay,
            });
            continue;
          }

          // Check if listing matches user criteria
          const matchesCriteria = userSettings.matchesCriteria(listing);
          loggers.app.debug("User criteria matching result", {
            userId: userSettings.userId,
            listingId: listing._id,
            matchesCriteria,
            userCriteria: {
              maxPrice: userSettings.criteria.maxPrice,
              locations: userSettings.criteria.locations,
              propertyTypes: userSettings.criteria.propertyTypes,
            },
          });

          if (!matchesCriteria) {
            loggers.app.debug(
              "Listing does not match user criteria, skipping",
              {
                userId: userSettings.userId,
                listingId: listing._id,
              }
            );
            continue;
          }

          // Check if user already applied to this listing
          const existingApplication = await ApplicationQueue.findOne({
            userId: userSettings.userId,
            listingId: listing._id,
          });

          if (existingApplication) {
            loggers.app.debug("User already has application for this listing", {
              userId: userSettings.userId,
              listingId: listing._id,
              existingApplicationId: existingApplication._id,
              existingStatus: existingApplication.status,
            });
            continue;
          }

          loggers.app.debug("Creating new application for user", {
            userId: userSettings.userId,
            listingId: listing._id,
          });

          // Generate application content
          const user = await User.findById(userSettings.userId);
          loggers.app.debug("User data retrieved for application generation", {
            userId: userSettings.userId,
            hasUserProfile: !!user.profile,
            userEmail: user.email,
          });

          const applicationContent = await this._generateApplicationContent(
            listing,
            user,
            userSettings
          );

          // Create queue item
          const priority = this._calculatePriority(listing, userSettings);
          const scheduledAt = this._calculateScheduledTime();

          loggers.app.debug("Application queue item being created", {
            userId: userSettings.userId,
            listingId: listing._id,
            priority,
            scheduledAt,
            contentWordCount: applicationContent.wordCount,
          });

          const queueItem = new ApplicationQueue({
            userId: userSettings.userId,
            listingId: listing._id,
            listingUrl: listing.url,
            priority,
            status: "pending",
            attempts: 0,
            maxAttempts: 3,
            scheduledAt,
            applicationData: {
              personalInfo: userSettings.personalInfo,
              documents: userSettings.documents.filter(
                (doc) => doc.uploaded && doc.verified
              ),
              preferences: userSettings.settings,
            },
            generatedContent: applicationContent,
            metadata: {
              formType: "unknown",
              detectionMethods: [],
              processingTime: 0,
            },
          });

          await queueItem.save();
          createdApplications.push(queueItem);

          loggers.app.debug("Application queue item saved successfully", {
            queueItemId: queueItem._id,
            userId: userSettings.userId,
            listingId: listing._id,
          });

          loggers.app.info(
            `Created auto-application for user ${userSettings.userId}, listing ${listing._id}`
          );
        } catch (error) {
          loggers.app.error(
            `Error processing listing ${listing._id} for user ${userSettings.userId}:`,
            error
          );
          loggers.app.debug("Individual user processing failed", {
            userId: userSettings.userId,
            listingId: listing._id,
            errorMessage: error.message,
            errorStack: error.stack,
          });
          // Continue with other users
        }
      }

      loggers.app.info(
        `Created ${createdApplications.length} auto-applications for listing ${listing._id}`
      );
      loggers.app.debug("Listing processing completed", {
        listingId: listing._id,
        totalCreated: createdApplications.length,
        totalActiveUsers: activeUsers.length,
        successRate: `${(
          (createdApplications.length / activeUsers.length) * 
          100
        ).toFixed(1)}%`,
      });

      return createdApplications;
    } catch (error) {
      loggers.app.error(`Error processing new listing ${listing._id}:`, error);
      loggers.app.debug("Listing processing failed completely", {
        listingId: listing._id,
        errorMessage: error.message,
        errorStack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Generate application content using AI service
   * @param {Object} listing - Listing data
   * @param {Object} user - User data
   * @param {Object} settings - Auto-application settings
   * @returns {Promise<Object>} Generated application content
   */
  async _generateApplicationContent(listing, user, settings) {
    loggers.app.debug("Starting application content generation", {
      listingId: listing._id || listing.id,
      userId: user._id,
      template: settings.settings.applicationTemplate,
      language: settings.settings.language,
    });

    try {
      // Prepare user profile for AI service
      const userProfile = {
        name:
          settings.personalInfo.fullName ||
          user.profile?.fullName ||
          user.email,
        income:
          settings.personalInfo.monthlyIncome ||
          user.profile?.employment?.monthlyIncome,
        occupation:
          settings.personalInfo.occupation ||
          user.profile?.employment?.occupation ||
          "Professional",
        employer:
          settings.personalInfo.employer || user.profile?.employment?.employer,
        nationality:
          settings.personalInfo.nationality || user.profile?.nationality,
        moveInDate: settings.personalInfo.moveInDate,
        leaseDuration: settings.personalInfo.leaseDuration,
        numberOfOccupants: settings.personalInfo.numberOfOccupants || 1,
        hasGuarantor: settings.personalInfo.hasGuarantor || false,
      };

      loggers.app.debug("User profile prepared for AI service", {
        userId: user._id,
        hasName: !!userProfile.name,
        hasIncome: !!userProfile.income,
        occupation: userProfile.occupation,
        numberOfOccupants: userProfile.numberOfOccupants,
        hasGuarantor: userProfile.hasGuarantor,
      });

      // Generate application message using enhanced AI service
      const language = settings.settings.language || "dutch";
      loggers.app.debug("Calling AI service for application generation", {
        language,
        listingTitle: listing.title,
        listingLocation: listing.location,
        template: settings.settings.applicationTemplate,
      });

      const aiResponse = await aiService.generateAutoApplicationLetter(
        listing,
        userProfile,
        settings.settings,
        language
      );

      loggers.app.debug("AI service response received", {
        hasSubject: !!aiResponse.subject,
        hasMessage: !!aiResponse.message,
        wordCount: aiResponse.wordCount,
        template: aiResponse.template,
        personalizedElementsCount: aiResponse.personalizedElements?.length || 0,
        estimatedReadTime: aiResponse.estimatedReadTime,
      });

      const result = {
        subject: aiResponse.subject || `Application for ${listing.title}`,
        message: aiResponse.message,
        personalizedElements: aiResponse.personalizedElements || [],
        template: aiResponse.template,
        language: aiResponse.language,
        wordCount: aiResponse.wordCount,
        estimatedReadTime: aiResponse.estimatedReadTime,
        generatedAt: aiResponse.generatedAt,
      };

      loggers.app.debug(
        "Application content generation completed successfully",
        {
          listingId: listing._id || listing.id,
          userId: user._id,
          finalWordCount: result.wordCount,
          finalLanguage: result.language,
          subjectLength: result.subject?.length || 0,
        }
      );

      return result;
    } catch (error) {
      loggers.app.error("Error generating application content:", error);
      loggers.app.debug(
        "AI generation failed, falling back to basic template",
        {
          listingId: listing._id || listing.id,
          userId: user._id,
          errorMessage: error.message,
          errorType: error.name,
        }
      );

      // Fallback to basic template if AI fails
      const fallbackContent = this._generateFallbackContent(
        listing,
        user,
        settings
      );
      loggers.app.debug("Fallback content generated", {
        listingId: listing._id || listing.id,
        userId: user._id,
        fallbackTemplate: fallbackContent.template,
        fallbackSubject: fallbackContent.subject,
      });

      return fallbackContent;
    }
  }

  /**
   * Generate fallback application content when AI fails
   * @param {Object} listing - Listing data
   * @param {Object} user - User data
   * @param {Object} settings - Auto-application settings
   * @returns {Object} Basic application content
   */
  _generateFallbackContent(listing, user, settings) {
    const name =
      settings.personalInfo.fullName || user.profile?.fullName || "Applicant";
    const occupation =
      settings.personalInfo.occupation ||
      user.profile?.employment?.occupation ||
      "Professional";

    const message = `Dear landlord,

I am interested in applying for the property at ${listing.location} (${listing.title}).

I am a ${occupation} with a stable income and I am looking for a new home. I believe this property would be perfect for my needs.

I am available for a viewing at your convenience and can provide all necessary documentation.

Thank you for your consideration.

Best regards,
${name}`;

    return {
      subject: `Application for ${listing.title}`,
      message,
      personalizedElements: ["Basic template"],
      template: "fallback",
      language: settings.settings.language,
      generatedAt: new Date(),
    };
  }

  /**
   * Calculate priority for application queue
   * @param {Object} listing - Listing data
   * @param {Object} settings - User settings
   * @returns {number} Priority score (higher = more priority)
   */
  _calculatePriority(listing, settings) {
    let priority = 50; // Base priority
    let hasPreferredLocation = false;
    let hasPreferredType = false;
    
    loggers.app.debug("Starting priority calculation", {
      listingId: listing._id || listing.id,
      basePriority: priority,
      listingPrice: listing.price,
      listingLocation: listing.location,
    });

    // Price-based priority (lower price = higher priority)
    if (listing.price && settings.criteria.maxPrice) {
      const priceMatch = listing.price.match(/[\d,]+/);
      if (priceMatch) {
        const listingPrice = parseInt(priceMatch[0].replace(/,/g, ""));
        const priceRatio = listingPrice / settings.criteria.maxPrice;
        const priceBonus = Math.max(0, (1 - priceRatio) * 20);
        priority += priceBonus;
        loggers.app.debug("Price-based priority calculated", {
          listingPrice,
          maxPrice: settings.criteria.maxPrice,
          priceRatio,
          priceBonus,
          newPriority: priority,
        });
      }
    }

    // Location-based priority
    if (settings.criteria.locations.length > 0) {
      hasPreferredLocation = settings.criteria.locations.some(
        (location) =>
          listing.location.toLowerCase().includes(location.toLowerCase())
      );
      if (hasPreferredLocation) {
        priority += 15;
        loggers.app.debug("Location preference bonus applied", {
          preferredLocations: settings.criteria.locations,
          listingLocation: listing.location,
          locationBonus: 15,
          newPriority: priority,
        });
      }
    }

    // Property type priority
    if (settings.criteria.propertyTypes.length > 0) {
      hasPreferredType = settings.criteria.propertyTypes.some(
        (type) =>
          listing.propertyType &&
          listing.propertyType.toLowerCase().includes(type.toLowerCase())
      );
      if (hasPreferredType) {
        priority += 10;
        loggers.app.debug("Property type preference bonus applied", {
          preferredTypes: settings.criteria.propertyTypes,
          listingType: listing.propertyType,
          typeBonus: 10,
          newPriority: priority,
        });
      }
    }

    // Recency priority (newer listings get higher priority)
    const listingAge = Date.now() - new Date(listing.dateAdded).getTime();
    const ageInHours = listingAge / (1000 * 60 * 60);
    let recencyBonus = 0;
    if (ageInHours < 1) {
      recencyBonus = 20; // Very new listing
    } else if (ageInHours < 6) {
      recencyBonus = 10; // Recent listing
    }
    priority += recencyBonus;

    loggers.app.debug("Recency bonus calculated", {
      listingAge: `${ageInHours.toFixed(2)} hours`,
      recencyBonus,
      newPriority: priority,
    });

    const finalPriority = Math.round(priority);
    loggers.app.debug("Priority calculation completed", {
      listingId: listing._id || listing.id,
      finalPriority,
      calculations: {
        base: 50,
        priceBonus:
          priority -
          50 -
          (recencyBonus || 0) -
          (hasPreferredLocation ? 15 : 0) -
          (hasPreferredType ? 10 : 0),
        locationBonus: hasPreferredLocation ? 15 : 0,
        typeBonus: hasPreferredType ? 10 : 0,
        recencyBonus,
      },
    });

    return finalPriority;
  }

  /**
   * Calculate scheduled time for application with random delay
   * @returns {Date} Scheduled time
   */
  _calculateScheduledTime() {
    const delay =
      Math.random() *
        (this.MAX_APPLICATION_DELAY_MS - this.MIN_APPLICATION_DELAY_MS) +
      this.MIN_APPLICATION_DELAY_MS;

    const scheduledTime = new Date(Date.now() + delay);

    loggers.app.debug("Application scheduling calculated", {
      delayMs: Math.round(delay),
      delayMinutes: Math.round(delay / (1000 * 60)),
      minDelayMs: this.MIN_APPLICATION_DELAY_MS,
      maxDelayMs: this.MAX_APPLICATION_DELAY_MS,
      scheduledTime: scheduledTime.toISOString(),
    });

    return scheduledTime;
  }

  /**
   * Get application status for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Application status
   */
  async getApplicationStatus(userId) {
    try {
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        return {
          enabled: false,
          isActive: false,
          statistics: {
            totalApplications: 0,
            successfulApplications: 0,
            pendingApplications: 0,
            dailyApplicationCount: 0,
            successRate: 0,
          },
        };
      }

      // Get recent applications
      const recentApplications = await ApplicationQueue.find({
        userId,
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
      })
        .sort({ createdAt: -1 })
        .limit(10)
        .populate("listingId", "title location price");

      return {
        enabled: autoSettings.enabled,
        isActive: autoSettings.status.isActive,
        canAutoApply: autoSettings.canAutoApply,
        dailyApplicationsRemaining: autoSettings.dailyApplicationsRemaining,
        statistics: autoSettings.statistics,
        status: {
          pausedReason: autoSettings.status.pausedReason,
          pausedUntil: autoSettings.status.pausedUntil,
          lastProcessed: autoSettings.status.lastProcessed,
          errorCount: autoSettings.status.errorCount,
          lastError: autoSettings.status.lastError,
        },
        recentApplications: recentApplications.map((app) => ({
          id: app._id,
          listingTitle: app.listingId?.title || "Unknown",
          listingLocation: app.listingId?.location || "Unknown",
          status: app.status,
          scheduledAt: app.scheduledAt,
          processedAt: app.processedAt,
          attempts: app.attempts,
        })),
      };
    } catch (error) {
      loggers.app.error(
        `Error getting application status for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Start the processing interval
   * @private
   */
  _startProcessing() {
    if (!this.processingInterval) {
      this.processingInterval = setInterval(() => {
        this._processQueue().catch((error) => {
          loggers.app.error("Error in queue processing interval:", error);
        });
      }, this.PROCESSING_INTERVAL_MS);

      loggers.app.info("Auto-application processing started");
    }
  }

  /**
   * Stop the processing interval
   * @private
   */
  _stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      loggers.app.info("Auto-application processing stopped");
    }
  }

  /**
   * Process the application queue
   * @private
   */
  async _processQueue() {
    if (this.isProcessing) {
      loggers.app.debug("Queue processing already in progress, skipping");
      return; // Already processing
    }

    try {
      this.isProcessing = true;
      loggers.app.debug("Starting queue processing cycle");

      // Get pending applications that are ready to be processed
      const pendingApplications = await ApplicationQueue.find({
        status: "pending",
        scheduledAt: { $lte: new Date() },
      })
        .sort({ priority: -1, scheduledAt: 1 })
        .limit(10); // Process up to 10 at a time

      loggers.app.debug("Queue query completed", {
        totalPending: pendingApplications.length,
        queryLimit: 10,
        currentTime: new Date().toISOString(),
      });

      if (pendingApplications.length === 0) {
        loggers.app.debug("No pending applications ready for processing");
        return;
      }

      // Log the number of pending applications
      loggers.app.info(
        `🔄 Processing ${pendingApplications.length} pending applications`
      );
      loggers.app.info(
        `Processing ${pendingApplications.length} pending applications`
      );

      for (const application of pendingApplications) {
        try {
          loggers.app.info("🔄 Starting to process application:", {
            applicationId: application._id,
          });

          loggers.app.debug("Processing application", {
            applicationId: application._id,
            userId: application.userId,
            listingUrl: application.listingUrl, // Use listingUrl from ApplicationQueue
            priority: application.priority,
            scheduledAt: application.scheduledAt,
            attempts: application.attempts,
          });

          // Mark as processing
          loggers.app.info("🔄 Marking application as processing:", {
            applicationId: application._id,
          });
          application.status = "processing";
          application.processedAt = new Date();
          await application.save();
          loggers.app.debug("Application marked as processing", {
            applicationId: application._id,
          });

          // Check rate limiting before proceeding
          loggers.app.info("⏱️ Checking rate limits for application", {
            applicationId: application._id,
          });
          const RateLimitingService = require("./rateLimitingService");
          const rateLimiter = new RateLimitingService();
          const domain = rateLimiter.extractDomain(application.listingUrl); // Use listingUrl from ApplicationQueue

          loggers.app.debug("Checking rate limits", {
            applicationId: application._id,
            domain,
            userId: application.userId,
          });

          const rateLimitCheck = rateLimiter.canSubmitApplication(
            application.userId,
            domain
          );

          loggers.app.debug("Rate limit check result", {
            applicationId: application._id,
            allowed: rateLimitCheck.allowed,
            reason: rateLimitCheck.reason,
            retryAfter: rateLimitCheck.retryAfter,
          });

          if (!rateLimitCheck.allowed) {
            loggers.app.warn("⏱️ Application rate limited", {
              applicationId: application._id,
            });
            application.status = "rate_limited";
            application.statusReason = `Rate limit: ${rateLimitCheck.reason}`;
            application.retryAfter = new Date(
              Date.now() + rateLimitCheck.retryAfter
            );
            loggers.app.warn(
              `Application rate limited for user ${application.userId}: ${rateLimitCheck.reason}`
            );
            await application.save();
            loggers.app.debug("Application saved with rate limit status", {
              applicationId: application._id,
            });
            continue; // Skip to next application
          }

          // Get user and auto-application settings for form automation
          loggers.app.info("👤 Loading user and settings for application", {
            applicationId: application._id,
          });
          loggers.app.debug("Loading user and auto-application settings", {
            applicationId: application._id,
            userId: application.userId,
          });

          const user = await User.findById(application.userId);
          if (!user) {
            loggers.app.error("❌ User not found for application", {
              applicationId: application._id,
            });
            throw new Error("User not found");
          }
          loggers.app.info("✅ User found for application", {
            applicationId: application._id,
          });

          const autoSettings = await AutoApplicationSettings.findByUserId(
            application.userId
          );
          if (!autoSettings) {
            loggers.app.error("❌ Auto settings not found for application", {
              applicationId: application._id,
            });
            throw new Error("Auto-application settings not found");
          }
          loggers.app.info("✅ Auto settings found for application", {
            applicationId: application._id,
          });

          // Transform AutoApplicationSettings to userSettings format
          const userSettings = {
            formData: {
              firstName:
                autoSettings.personalInfo.fullName?.split(" ")[0] || "User",
              lastName:
                autoSettings.personalInfo.fullName
                  ?.split(" ")
                  .slice(1)
                  .join(" ") || "",
              email: autoSettings.personalInfo.email || user.email,
              phone: autoSettings.personalInfo.phone || "",
              age: autoSettings.personalInfo.age?.toString() || "25",
              occupation:
                autoSettings.personalInfo.occupation || "Professional",
              monthlyIncome:
                autoSettings.personalInfo.monthlyIncome?.toString() || "3000",
              additionalInfo: "Automated application via ZakMakelaar platform.",
            },
            messageTemplates: {
              default:
                application.generatedContent?.message ||
                `Dear landlord,\n\nI am interested in this property and would like to schedule a viewing.\n\nBest regards,\n${
                  autoSettings.personalInfo.fullName || user.name || user.email
                }`,
            },
          };

          loggers.app.debug("User settings prepared for form automation", {
            applicationId: application._id,
            userId: application.userId,
            hasFormData: !!userSettings.formData,
            formDataKeys: Object.keys(userSettings.formData),
          });

          // Trigger form automation engine
          loggers.app.info("🤖 Starting form automation for application", {
            applicationId: application._id,
          });
          loggers.app.debug("Initializing form automation", {
            applicationId: application._id,
          });
          const FormAutomationService = require("./formAutomationService");
          const formAutomation = new FormAutomationService();

          try {
            loggers.app.info("🚀 Calling submitApplication for application", {
              applicationId: application._id,
            });
            loggers.app.debug("Starting form submission", {
              applicationId: application._id,
              listingUrl: application.listingUrl, // Use listingUrl from ApplicationQueue
            });

            const result = await formAutomation.submitApplication(
              {
                ...application.toObject(), // Convert mongoose document to plain object
                propertyUrl: application.listingUrl, // Map listingUrl to propertyUrl for FormAutomationService
              },
              userSettings
            );

            loggers.app.info("✅ Form automation completed for application", {
              applicationId: application._id,
              result,
            });
            loggers.app.debug("Form submission completed successfully", {
              applicationId: application._id,
              result,
            });

            application.status = "completed";
            application.automationResult = result;

            // Record successful application for rate limiting
            rateLimiter.recordApplication(application.userId, domain);
            loggers.app.debug(
              "Rate limiter updated for successful application",
              {
                userId: application.userId,
                domain,
              }
            );

            // Create ApplicationResult record for successful application
            await this._createApplicationResult(application, result, null);

            loggers.app.info(
              `Form submitted successfully for application ${application._id}:`,
              result
            );
          } catch (error) {
            loggers.app.error("❌ Form automation error for application", {
              applicationId: application._id,
              error: error.message,
            });
            loggers.app.debug("Form automation error occurred", {
              applicationId: application._id,
              errorMessage: error.message,
              errorType: error.name,
            });

            // CAPTCHA handling removed - treat all errors as regular failures
            application.status = "failed";
            application.statusReason = error.message;
            
            // Create ApplicationResult record for failed application
            await this._createApplicationResult(application, null, error);
            
            loggers.app.debug("Application marked as failed", {
              applicationId: application._id,
              reason: error.message,
            });
            loggers.app.error(
              `Form automation failed for application ${application._id}:`,
              error
            );
          } finally {
            loggers.app.info("🧹 Cleaning up form automation for application", {
              applicationId: application._id,
            });
            loggers.app.debug("Cleaning up form automation resources", {
              applicationId: application._id,
            });
            await formAutomation.cleanup();
          }
          loggers.app.info("💾 Saving application status for application", {
            applicationId: application._id,
            status: application.status,
          });
          await application.save();
          loggers.app.debug("Application status saved to database", {
            applicationId: application._id,
            status: application.status,
          });

          // Update user statistics
          const userStatsSettings = await AutoApplicationSettings.findByUserId(
            application.userId
          );
          if (userStatsSettings) {
            loggers.app.debug("Updating user statistics", {
              userId: application.userId,
              currentCount: userStatsSettings.statistics.totalApplications,
            });
            await userStatsSettings.incrementApplicationCount();
            loggers.app.debug("User statistics updated successfully", {
              userId: application.userId,
            });
          } else {
            loggers.app.debug(
              "No auto settings found for user statistics update",
              { userId: application.userId }
            );
          }

          loggers.app.info(`Processed application ${application._id}`);
        } catch (error) {
          // Enhanced error logging for debugging
          loggers.app.error("🚨 Error in _processQueue for application", {
            applicationId: application._id,
            errorMessage: error.message,
            errorStack: error.stack,
            errorName: error.name,
          });

          loggers.app.debug("Error processing individual application", {
            applicationId: application._id,
            userId: application.userId,
            errorMessage: error.message,
            errorStack: error.stack,
            attempts: application.attempts,
          });

          // Use comprehensive error handling
          const recoveryResult = await this.errorHandlingService.handleError(
            error,
            {
              service: "AutoApplicationService",
              method: "processQueue",
              queueItemId: application._id,
              userId: application.userId,
              attemptNumber: application.attempts + 1,
              operation: "process_application",
            }
          );

          loggers.app.debug("Error handling service response", {
            applicationId: application._id,
            retryScheduled: recoveryResult.retryScheduled,
            recoveryAction: recoveryResult.action,
          });

          loggers.app.error(
            `Error processing application ${application._id}:`,
            error
          );

          // If error handling didn't schedule a retry, handle it manually
          if (!recoveryResult.retryScheduled) {
            application.status = "failed";
            application.statusReason = error.message; // Add statusReason for debugging
            application.attempts += 1;
            application.errors.push(error.message);
            
            // Create ApplicationResult record for failed application
            await this._createApplicationResult(application, null, error);
            
            await application.save();
            loggers.app.debug("Application marked as failed and saved", {
              applicationId: application._id,
              attempts: application.attempts,
              errorCount: application.errors.length,
              statusReason: application.statusReason, // Log the reason
            });
          }
        }
      }
    } catch (error) {
      loggers.app.error("Error processing application queue:", error);
      loggers.app.debug("Queue processing failed", {
        errorMessage: error.message,
        errorStack: error.stack,
        processingState: this.isProcessing,
      });
    } finally {
      this.isProcessing = false;
      loggers.app.debug("Queue processing cycle completed, releasing lock");
    }
  }

  /**
   * Validate settings update object (partial validation)
   * @param {Object} settings - Settings to validate
   * @private
   */
  _validateSettingsUpdate(settings) {
    if (!settings) {
      return; // No settings to validate
    }

    if (
      settings.maxApplicationsPerDay &&
      (settings.maxApplicationsPerDay < 1 ||
        settings.maxApplicationsPerDay > this.MAX_DAILY_APPLICATIONS)
    ) {
      throw new Error(
        `Max applications per day must be between 1 and ${this.MAX_DAILY_APPLICATIONS}`
      );
    }

    if (
      settings.applicationTemplate &&
      !["professional", "casual", "student", "expat"].includes(
        settings.applicationTemplate
      )
    ) {
      throw new Error("Invalid application template");
    }

    if (
      settings.language &&
      !["dutch", "english"].includes(settings.language)
    ) {
      throw new Error("Invalid language");
    }
  }

  /**
   * Validate settings object
   * @param {Object} settings - Settings to validate
   * @private
   */
  _validateSettings(settings) {
    if (!settings) {
      throw new Error("Settings are required");
    }

    if (
      settings.maxApplicationsPerDay &&
      (settings.maxApplicationsPerDay < 1 ||
        settings.maxApplicationsPerDay > this.MAX_DAILY_APPLICATIONS)
    ) {
      throw new Error(
        `Max applications per day must be between 1 and ${this.MAX_DAILY_APPLICATIONS}`
      );
    }

    if (
      settings.applicationTemplate &&
      !["professional", "casual", "student", "expat"].includes(
        settings.applicationTemplate
      )
    ) {
      throw new Error("Invalid application template");
    }

    if (
      settings.language &&
      !["dutch", "english"].includes(settings.language)
    ) {
      throw new Error("Invalid language");
    }

    if (settings.criteria) {
      if (!settings.criteria.maxPrice || settings.criteria.maxPrice <= 0) {
        throw new Error("Max price is required and must be greater than 0");
      }

      if (settings.criteria.minRooms && settings.criteria.minRooms < 1) {
        throw new Error("Min rooms must be at least 1");
      }

      if (settings.criteria.maxRooms && settings.criteria.maxRooms < 1) {
        throw new Error("Max rooms must be at least 1");
      }

      if (
        settings.criteria.minRooms &&
        settings.criteria.maxRooms &&
        settings.criteria.minRooms > settings.criteria.maxRooms
      ) {
        throw new Error("Min rooms cannot be greater than max rooms");
      }
    }
  }

  /**
   * Format settings response for API
   * @param {Object} autoSettings - Auto-application settings
   * @returns {Object} Formatted response
   * @private
   */
  _formatSettingsResponse(autoSettings) {
    return {
      id: autoSettings._id,
      userId: autoSettings.userId,
      enabled: autoSettings.enabled,
      isActive: autoSettings.status.isActive,
      canAutoApply: autoSettings.canAutoApply,
      settings: autoSettings.settings,
      criteria: autoSettings.criteria,
      personalInfo: {
        ...(autoSettings.personalInfo || {}),
        // Don't expose sensitive info in API response
        monthlyIncome:
          autoSettings.personalInfo && autoSettings.personalInfo.monthlyIncome
            ? "[HIDDEN]"
            : undefined,
      },
      documents: (autoSettings.documents || []).map((doc) => ({
        type: doc.type,
        required: doc.required,
        uploaded: doc.uploaded,
        verified: doc.verified,
      })),
      statistics: autoSettings.statistics,
      status: {
        isActive: autoSettings.status ? autoSettings.status.isActive : false,
        pausedReason: autoSettings.status
          ? autoSettings.status.pausedReason
          : undefined,
        pausedUntil: autoSettings.status
          ? autoSettings.status.pausedUntil
          : undefined,
        lastProcessed: autoSettings.status
          ? autoSettings.status.lastProcessed
          : undefined,
        errorCount: autoSettings.status ? autoSettings.status.errorCount : 0,
      },
      isProfileComplete: autoSettings.isProfileComplete,
      documentsComplete: autoSettings.documentsComplete,
      dailyApplicationsRemaining: autoSettings.dailyApplicationsRemaining,
      createdAt: autoSettings.createdAt,
      updatedAt: autoSettings.updatedAt,
    };
  }

  /**
   * Create ApplicationResult record for completed or failed applications
   * @param {Object} queueItem - Application queue item
   * @param {Object} result - Automation result (if successful)
   * @param {Error} error - Error object (if failed)
   * @returns {Promise<Object>} Created ApplicationResult
   * @private
   */
  async _createApplicationResult(queueItem, result = null, error = null) {
    try {
      loggers.app.debug("Creating ApplicationResult record", {
        applicationId: queueItem._id,
        userId: queueItem.userId,
        status: queueItem.status,
        hasResult: !!result,
        hasError: !!error,
      });

      const resultData = {
        userId: queueItem.userId,
        listingId: queueItem.listingId,
        queueItemId: queueItem._id,
        status: error ? "failed" : "submitted",
        submittedAt: new Date(),
      };

      // Add successful application data
      if (result && !error) {
        resultData.confirmationNumber = result.confirmationNumber || null;
        resultData.confirmationEmail = result.confirmationEmail || false;
        resultData.response = {
          success: true,
          message: result.message || "Application submitted successfully",
          redirectUrl: result.redirectUrl,
          responseCode: result.responseCode,
          responseTime: result.processingTime,
        };
        
        if (result.screenshots && result.screenshots.length > 0) {
          resultData.screenshots = result.screenshots.map(screenshot => ({
            type: screenshot.type || "confirmation",
            description: screenshot.description || "Application confirmation",
            timestamp: new Date(),
          }));
        }
      }

      // Add failure data
      if (error) {
        resultData.response = {
          success: false,
          message: error.message,
          responseTime: 0,
        };
        
        resultData.error = {
          category: this._categorizeError(error),
          code: error.code || "SUBMISSION_FAILED",
          message: error.message,
          details: error.stack,
          retryable: queueItem.attempts < queueItem.maxAttempts,
        };
      }

      // Add form data that was submitted
      if (queueItem.applicationData) {
        resultData.formData = {
          personalInfo: queueItem.applicationData.personalInfo || {},
          applicationMessage: queueItem.generatedContent?.message || "",
          documentsSubmitted: queueItem.applicationData.documents?.map(doc => ({
            type: doc.type,
            filename: doc.filename,
            size: doc.size || 0,
            submitted: true,
          })) || [],
        };
      }

      // Add processing metrics
      resultData.metrics = {
        processingTime: Date.now() - queueItem.createdAt.getTime(),
        formComplexity: result?.formComplexity || "moderate",
        formDetectionTime: result?.formDetectionTime || 0,
        formFillingTime: result?.formFillingTime || 0,
        submissionTime: result?.submissionTime || 0,
        fieldsDetected: result?.fieldsDetected || 0,
        fieldsSuccessfullyFilled: result?.fieldsSuccessfullyFilled || 0,
        successProbability: result?.successProbability || 50,
        antiDetectionMeasuresUsed: result?.antiDetectionMeasures || [],
      };

      // Add AI-generated content
      if (queueItem.generatedContent) {
        resultData.aiContent = {
          subject: queueItem.generatedContent.subject,
          message: queueItem.generatedContent.message,
          personalizedElements: queueItem.generatedContent.personalizedElements || [],
          template: queueItem.generatedContent.template,
          language: queueItem.generatedContent.language,
          confidence: queueItem.generatedContent.confidence || 80,
          generatedAt: queueItem.generatedContent.generatedAt || queueItem.createdAt,
        };
      }

      // Add listing snapshot
      if (queueItem.listingSnapshot) {
        resultData.listingSnapshot = {
          title: queueItem.listingSnapshot.title,
          address: queueItem.listingSnapshot.address,
          price: queueItem.listingSnapshot.price,
          rooms: queueItem.listingSnapshot.rooms,
          size: queueItem.listingSnapshot.size,
          propertyType: queueItem.listingSnapshot.propertyType,
          url: queueItem.listingUrl,
        };
      }

      // Add analytics data
      resultData.analytics = {
        timeToSubmission: Date.now() - queueItem.createdAt.getTime(),
        queueWaitTime: queueItem.processedAt ? 
          queueItem.processedAt.getTime() - queueItem.createdAt.getTime() : 0,
        successFactors: [],
        marketCondition: "moderate_demand",
        competitionLevel: "moderate",
      };

      // Add system metadata
      resultData.metadata = {
        applicationVersion: "1.0.0",
        platform: process.platform,
        serverId: process.env.SERVER_ID || "unknown",
        processingNode: process.env.NODE_ENV || "development",
      };

      const applicationResult = new ApplicationResult(resultData);
      await applicationResult.save();

      loggers.app.info(`ApplicationResult created successfully: ${applicationResult._id}`, {
        applicationId: queueItem._id,
        resultId: applicationResult._id,
        status: applicationResult.status,
      });

      return applicationResult;
    } catch (createError) {
      loggers.app.error(
        `Error creating ApplicationResult for application ${queueItem._id}:`,
        createError
      );
      // Don't throw - we don't want to fail the entire process because of result creation
      return null;
    }
  }

  /**
   * Categorize error for ApplicationResult
   * @param {Error} error - Error object
   * @returns {string} Error category
   * @private
   */
  _categorizeError(error) {
    const errorMessage = error.message.toLowerCase();

    if (
      errorMessage.includes("timeout") ||
      errorMessage.includes("navigation timeout")
    ) {
      return "network";
    }

    if (
      errorMessage.includes("network") ||
      errorMessage.includes("connection") ||
      errorMessage.includes("fetch")
    ) {
      return "network";
    }

    if (
      errorMessage.includes("captcha") ||
      errorMessage.includes("verification")
    ) {
      return "detection";
    }

    if (
      errorMessage.includes("blocked") ||
      errorMessage.includes("cloudflare") ||
      errorMessage.includes("security check")
    ) {
      return "detection";
    }

    if (
      errorMessage.includes("form") ||
      errorMessage.includes("field") ||
      errorMessage.includes("selector")
    ) {
      return "form";
    }

    if (
      errorMessage.includes("validation") ||
      errorMessage.includes("required") ||
      errorMessage.includes("invalid")
    ) {
      return "validation";
    }

    return "system";
  }

  /**
   * Shutdown the service gracefully
   */
  shutdown() {
    this._stopProcessing();
    loggers.app.info("AutoApplicationService shutdown complete");
  }
  /**
   * Enable auto-application for a user (simplified version)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated settings
   */
  async enableAutoApplication(userId) {
    try {
      loggers.app.info(`Enabling auto-application for user ${userId}`);

      const settings = await this.updateSettings(userId, { enabled: true });

      // Send WebSocket notification (temporarily disabled for debugging)
      try {
        websocketService.sendAutoApplicationUpdate(userId, {
          action: "enabled",
          message: "Auto-application has been enabled",
          settings: settings,
        });
      } catch (wsError) {
        loggers.app.warn("WebSocket notification failed:", wsError);
        // Don't throw, just log the warning
      }

      return settings;
    } catch (error) {
      loggers.app.error(
        `Error enabling auto-application for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Disable auto-application for a user
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async disableAutoApplication(userId) {
    try {
      loggers.app.info(`Disabling auto-application for user ${userId}`);

      await this.updateSettings(userId, { enabled: false });

      // Send WebSocket notification (temporarily disabled for debugging)
      try {
        websocketService.sendAutoApplicationUpdate(userId, {
          action: "disabled",
          message: "Auto-application has been disabled",
        });
      } catch (wsError) {
        loggers.app.warn("WebSocket notification failed:", wsError);
        // Don't throw, just log the warning
      }

      loggers.app.info(`Auto-application disabled for user ${userId}`);
    } catch (error) {
      loggers.app.error(
        `Error disabling auto-application for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get application history with detailed status
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Application history
   */
  async getApplicationHistory(userId, options = {}) {
    try {
      const { status, limit = 20, offset = 0, dateFrom, dateTo } = options;

      // Build query
      const query = { userId };
      if (status) query.status = status;
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Get applications with property details
      const applications = await ApplicationResult.find(query)
        .sort({ createdAt: -1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .populate("listingId", "title location price")
        .lean();

      // Get total count
      const total = await ApplicationResult.countDocuments(query);

      // Calculate summary statistics
      const summary = await this._calculateApplicationSummary(
        userId,
        dateFrom,
        dateTo
      );

      // Add timeline information for each application
      const applicationsWithTimeline = applications.map((app) => ({
        ...app,
        property: app.listingId || {
          title: "Property details unavailable",
          location: "Unknown",
          price: "Unknown",
        },
        timeline: this._generateApplicationTimeline(app),
      }));

      return {
        applications: applicationsWithTimeline,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        summary,
      };
    } catch (error) {
      loggers.app.error(
        `Error getting application history for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Upload documents for auto-application
   * @param {string} userId - User ID
   * @param {Object} uploadData - Upload data
   * @returns {Promise<Object>} Upload result
   */
  async uploadDocuments(userId, uploadData) {
    try {
      const { files, documentType, expiryDate } = uploadData;

      loggers.app.info(
        `Uploading ${files.length} documents for user ${userId}, type: ${documentType}`
      );

      const uploadedDocuments = [];
      const errors = [];

      // Process each file
      for (const file of files) {
        try {
          const document = await documentVaultService.uploadDocument(
            userId,
            file,
            documentType,
            { expiryDate }
          );
          uploadedDocuments.push({
            documentId: document._id,
            filename: document.filename,
            type: document.type,
            size: document.size,
            uploadedAt: document.createdAt,
          });
        } catch (error) {
          errors.push({
            filename: file.originalname,
            error: error.message,
          });
        }
      }

      // Update profile completeness
      const profileCompleteness = await this._calculateProfileCompleteness(
        userId
      );

      // Send WebSocket notification
      websocketService.sendDocumentUpdate(userId, {
        action: "uploaded",
        documents: uploadedDocuments,
        profileCompleteness,
        errors: errors.length > 0 ? errors : undefined,
      });

      return {
        uploadedDocuments,
        profileCompleteness,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      loggers.app.error(`Error uploading documents for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's documents for auto-application
   * @param {string} userId - User ID
   * @param {string} type - Document type filter
   * @returns {Promise<Object>} Documents data
   */
  async getDocuments(userId, type) {
    try {
      const documents = await documentVaultService.getDocuments(userId, type);

      const requiredDocuments = [
        {
          type: "income_proof",
          required: true,
          description: "Proof of income (salary slip, contract)",
        },
        {
          type: "employment_contract",
          required: true,
          description: "Employment contract or work agreement",
        },
        {
          type: "bank_statement",
          required: true,
          description: "Recent bank statements (last 3 months)",
        },
        {
          type: "id_document",
          required: true,
          description: "Valid ID document (passport, ID card)",
        },
        {
          type: "rental_reference",
          required: false,
          description: "Previous rental reference (if applicable)",
        },
      ];

      // Map uploaded documents to required documents
      const documentsWithStatus = requiredDocuments.map((reqDoc) => ({
        ...reqDoc,
        uploaded: documents.some((doc) => doc.type === reqDoc.type),
        documents: documents.filter((doc) => doc.type === reqDoc.type),
      }));

      const completeness = await this._calculateDocumentCompleteness(userId);

      return {
        documents: documents.map((doc) => ({
          documentId: doc._id,
          filename: doc.filename,
          type: doc.type,
          size: doc.size,
          uploadedAt: doc.createdAt,
          expiryDate: doc.expiryDate,
          verified: doc.verified,
        })),
        requiredDocuments: documentsWithStatus,
        completeness,
      };
    } catch (error) {
      loggers.app.error(`Error getting documents for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @returns {Promise<Object>} Result
   */
  async deleteDocument(userId, documentId) {
    try {
      await documentVaultService.deleteDocument(documentId, userId);

      const profileCompleteness = await this._calculateProfileCompleteness(
        userId
      );

      // Send WebSocket notification
      websocketService.sendDocumentUpdate(userId, {
        action: "deleted",
        documentId,
        profileCompleteness,
      });

      return { profileCompleteness };
    } catch (error) {
      loggers.app.error(
        `Error deleting document ${documentId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get real-time status for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Status data
   */
  async getStatus(userId) {
    try {
      const settings = await this.getSettings(userId);
      const queueStats = await this._getQueueStats(userId);
      const todaysActivity = await this._getTodaysActivity(userId);
      const systemHealth = await this._getSystemHealth(userId);
      
      // Determine if system is actually active
      const isEnabled = settings?.enabled || false;
      const hasQueueItems = (queueStats.pending + queueStats.processing) > 0;
      const hasRemainingApplications = todaysActivity.applicationsRemaining > 0;
      const isActive = settings?.isActive || false;
      
      // Determine current activity
      let currentActivity;
      if (!isEnabled) {
        currentActivity = 'Autonomous mode is disabled';
      } else if (settings?.status?.pausedReason) {
        currentActivity = `Paused: ${settings.status.pausedReason}`;
      } else if (!hasRemainingApplications) {
        currentActivity = `Daily limit reached (${todaysActivity.applicationsSubmitted}/${todaysActivity.dailyLimit})`;
      } else if (queueStats.processing > 0) {
        currentActivity = `Processing ${queueStats.processing} application${queueStats.processing > 1 ? 's' : ''}`;
      } else if (queueStats.pending > 0) {
        currentActivity = `${queueStats.pending} application${queueStats.pending > 1 ? 's' : ''} pending`;
      } else {
        currentActivity = 'Monitoring for new listings';
      }

      return {
        isEnabled,
        isActive,
        isPaused: settings?.status?.pausedReason ? true : false,
        pausedReason: settings?.status?.pausedReason,
        pausedUntil: settings?.status?.pausedUntil,
        currentActivity,
        applicationsToday: todaysActivity.applicationsSubmitted,
        applicationsThisWeek: 0, // TODO: Calculate this if needed
        currentQueue: queueStats,
        todaysActivity,
        systemHealth,
      };
    } catch (error) {
      loggers.app.error(`Error getting status for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate application summary statistics
   * @private
   */
  async _calculateApplicationSummary(userId, dateFrom, dateTo) {
    const query = { userId };
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const [total, successful, failed, pending] = await Promise.all([
      ApplicationResult.countDocuments(query),
      ApplicationResult.countDocuments({ ...query, status: "success" }),
      ApplicationResult.countDocuments({ ...query, status: "failed" }),
      ApplicationQueue.countDocuments({ userId, status: "pending" }),
    ]);

    return {
      totalApplications: total,
      successfulApplications: successful,
      failedApplications: failed,
      pendingApplications: pending,
      successRate: total > 0 ? Math.round((successful / total) * 100) : 0,
    };
  }

  /**
   * Generate application timeline
   * @private
   */
  _generateApplicationTimeline(application) {
    const timeline = [
      {
        status: "queued",
        timestamp: application.createdAt,
        message: "Application added to queue",
      },
    ];

    if (application.status === "success") {
      timeline.push({
        status: "submitted",
        timestamp: application.submittedAt || application.updatedAt,
        message: "Application submitted successfully",
      });
    } else if (application.status === "failed") {
      timeline.push({
        status: "failed",
        timestamp: application.updatedAt,
        message: application.errorDetails?.message || "Application failed",
      });
    }

    return timeline;
  }

  /**
   * Calculate profile completeness
   * @private
   */
  async _calculateProfileCompleteness(userId) {
    try {
      const settings = await this.getSettings(userId);
      const documents = await documentVaultService.getDocuments(userId);

      let completeness = 0;

      // Personal info completeness (40%)
      if (settings?.applicationTemplate?.personalInfo) {
        const personalInfo = settings.applicationTemplate.personalInfo;
        const requiredFields = [
          "firstName",
          "lastName",
          "email",
          "phone",
          "dateOfBirth",
        ];
        const completedFields = requiredFields.filter(
          (field) => personalInfo[field]
        );
        completeness += (completedFields.length / requiredFields.length) * 40;
      }

      // Document completeness (60%)
      const requiredDocTypes = [
        "income_proof",
        "employment_contract",
        "bank_statement",
        "id_document",
      ];
      const uploadedTypes = [...new Set(documents.map((doc) => doc.type))];
      const docCompleteness = requiredDocTypes.filter((type) =>
        uploadedTypes.includes(type)
      ).length;
      completeness += (docCompleteness / requiredDocTypes.length) * 60;

      return Math.round(completeness);
    } catch (error) {
      loggers.app.error(
        `Error calculating profile completeness for user ${userId}:`,
        error
      );
      return 0;
    }
  }

  /**
   * Calculate document completeness
   * @private
   */
  async _calculateDocumentCompleteness(userId) {
    try {
      const documents = await documentVaultService.getDocuments(userId);
      const requiredDocTypes = [
        "income_proof",
        "employment_contract",
        "bank_statement",
        "id_document",
      ];
      const uploadedTypes = [...new Set(documents.map((doc) => doc.type))];
      const completeness = requiredDocTypes.filter((type) =>
        uploadedTypes.includes(type)
      ).length;
      return Math.round((completeness / requiredDocTypes.length) * 100);
    } catch (error) {
      loggers.app.error(
        `Error calculating document completeness for user ${userId}:`,
        error
      );
      return 0;
    }
  }

  /**
   * Get queue statistics
   * @private
   */
  async _getQueueStats(userId) {
    try {
      const [pending, processing] = await Promise.all([
        ApplicationQueue.countDocuments({ userId, status: "pending" }),
        ApplicationQueue.countDocuments({ userId, status: "processing" }),
      ]);

      const nextScheduled = await ApplicationQueue.findOne(
        { userId, status: "pending" },
        { scheduledAt: 1 }
      ).sort({ scheduledAt: 1 });

      return {
        pending,
        processing,
        nextScheduled: nextScheduled?.scheduledAt,
      };
    } catch (error) {
      loggers.app.error(`Error getting queue stats for user ${userId}:`, error);
      return { pending: 0, processing: 0, nextScheduled: null };
    }
  }

  /**
   * Get today's activity
   * @private
   */
  async _getTodaysActivity(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const applicationsSubmitted = await ApplicationResult.countDocuments({
        userId,
        createdAt: { $gte: today, $lt: tomorrow },
      });

      const settings = await this.getSettings(userId);
      const dailyLimit =
        settings?.maxApplicationsPerDay || this.MAX_DAILY_APPLICATIONS;

      return {
        applicationsSubmitted,
        applicationsRemaining: Math.max(0, dailyLimit - applicationsSubmitted),
        dailyLimit,
        resetTime: tomorrow,
      };
    } catch (error) {
      loggers.app.error(
        `Error getting today's activity for user ${userId}:`,
        error
      );
      return {
        applicationsSubmitted: 0,
        applicationsRemaining: 0,
        dailyLimit: this.MAX_DAILY_APPLICATIONS,
        resetTime: new Date(),
      };
    }
  }

  /**
   * Get system health
   * @private
   */
  async _getSystemHealth(userId) {
    try {
      const lastWeek = new Date();
      lastWeek.setDate(lastWeek.getDate() - 7);

      const [lastSuccessful, recentErrors, recentBlocking] = await Promise.all([
        ApplicationResult.findOne(
          { 
            userId, 
            $or: [
              { status: "success" },
              { status: "submitted", isSuccessful: true }
            ]
          },
          { submittedAt: 1 }
        ).sort({ submittedAt: -1 }),
        ApplicationResult.countDocuments({
          userId,
          status: "failed",
          createdAt: { $gte: lastWeek },
        }),
        ApplicationResult.countDocuments({
          userId,
          "errorDetails.blockingDetected": true,
          createdAt: { $gte: lastWeek },
        }),
      ]);

      let status = "healthy";
      if (recentBlocking > 0) status = "error";
      else if (recentErrors > 5) status = "warning";

      return {
        status,
        lastSuccessfulApplication: lastSuccessful?.submittedAt,
        recentErrors,
        blockingDetected: recentBlocking > 0,
      };
    } catch (error) {
      loggers.app.error(
        `Error getting system health for user ${userId}:`,
        error
      );
      return {
        status: "error",
        lastSuccessfulApplication: null,
        recentErrors: 0,
        blockingDetected: false,
      };
    }
  }
  /**
   * Get user's auto-application settings
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User settings
   */
  async getSettings(userId) {
    try {
      loggers.app.info(`Getting settings for user ${userId}`);

      const settings = await AutoApplicationSettings.findByUserId(userId);

      if (!settings) {
        // Return default settings if none exist
        return {
          userId,
          enabled: false,
          settings: {
            maxApplicationsPerDay: 5,
            applicationTemplate: "professional",
            autoSubmit: false,
            requireManualReview: true,
            notificationPreferences: {
              immediate: true,
              daily: true,
              weekly: false,
            },
            language: "english",
          },
          criteria: {
            maxPrice: 2000,
            minRooms: 1,
            maxRooms: 5,
            propertyTypes: [],
            locations: [],
            excludeKeywords: [],
            includeKeywords: [],
            minSize: 30,
            maxSize: 150,
          },
          personalInfo: {
            fullName: "",
            email: "",
            phone: "",
          },
          documents: [],
          statistics: {
            totalApplications: 0,
            successfulApplications: 0,
            pendingApplications: 0,
            rejectedApplications: 0,
            averageResponseTime: 0,
          },
          status: {
            isActive: false,
            currentQueue: 0,
            dailyApplicationsUsed: 0,
            weeklyApplicationsUsed: 0,
            monthlyApplicationsUsed: 0,
            lastResetDate: new Date(),
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }

      return settings;
    } catch (error) {
      loggers.app.error("Error getting settings:", error);
      throw error;
    }
  }

  /**
   * Update user's auto-application settings
   * @param {string} userId - User ID
   * @param {Object} updates - Settings updates
   * @returns {Promise<Object>} Updated settings
   */
  async updateSettings(userId, updates) {
    try {
      loggers.app.info(`Updating settings for user ${userId}`, { updates });

      let settings = await AutoApplicationSettings.findByUserId(userId);
      loggers.app.info(`Found existing settings: ${!!settings}`);

      if (!settings) {
        loggers.app.info("Creating new settings for user");
        // Create new settings if none exist
        settings = new AutoApplicationSettings({
          userId,
          enabled: false,
          settings: {},
          criteria: {},
          personalInfo: {},
          documents: [],
          statistics: {},
          status: {},
        });
      }

      const updateData = updates.data || updates;

      // Update the settings with provided updates
      if (updateData.enabled !== undefined) {
        loggers.app.info(`Setting enabled to: ${updateData.enabled}`);
        settings.enabled = updateData.enabled;
      }

      if (updateData.settings) {
        settings.settings = { ...settings.settings, ...updateData.settings };
      }

      if (updateData.criteria) {
        settings.criteria = { ...settings.criteria, ...updateData.criteria };
      }

      if (updateData.personalInfo) {
        settings.personalInfo = {
          ...settings.personalInfo,
          ...updateData.personalInfo,
        };
      }

      if (updateData.documents) {
        settings.documents = updateData.documents;
      }

      settings.updatedAt = new Date();

      loggers.app.info("About to save settings");
      await settings.save();
      loggers.app.info("Settings saved successfully");

      return settings;
    } catch (error) {
      loggers.app.error("Error updating settings:", error);
      loggers.app.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      throw error;
    }
  }

  /**
   * Get application results for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Application results
   */
  async getApplicationResults(userId, options = {}) {
    try {
      loggers.app.info(`Getting application results for user ${userId}`);

      const query = { userId };

      if (options.status) {
        query.status = options.status;
      }

      if (options.dateFrom || options.dateTo) {
        query.submittedAt = {};
        if (options.dateFrom) {
          query.submittedAt.$gte = new Date(options.dateFrom);
        }
        if (options.dateTo) {
          query.submittedAt.$lte = new Date(options.dateTo);
        }
      }

      const limit = options.limit || 20;
      const offset = options.offset || 0;

      const results = await ApplicationResult.find(query)
        .sort({ submittedAt: -1 })
        .limit(limit)
        .skip(offset);

      const total = await ApplicationResult.countDocuments(query);

      return {
        results,
        total,
        limit,
        offset,
      };
    } catch (error) {
      loggers.app.error("Error getting application results:", error);
      throw error;
    }
  }

  /**
   * Get application statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Application statistics
   */
  async getApplicationStats(userId) {
    try {
      loggers.app.info(`Getting application stats for user ${userId}`);

      const totalApplications = await ApplicationResult.countDocuments({
        userId,
      });
      const successfulApplications = await ApplicationResult.countDocuments({
        userId,
        $or: [
          { status: "success" },
          { $and: [{ status: "submitted" }, { "response.success": true }] }
        ]
      });
      const failedApplications = await ApplicationResult.countDocuments({
        userId,
        status: "failed",
      });
      const pendingApplications = await ApplicationQueue.countDocuments({
        userId,
        status: "pending",
      });

      // Calculate success rate
      const successRate =
        totalApplications > 0
          ? Math.round((successfulApplications / totalApplications) * 100)
          : 0;

      // Calculate average response time
      const responseTimeResults = await ApplicationResult.aggregate([
        { $match: { userId: userId, responseTime: { $exists: true } } },
        { $group: { _id: null, avgResponseTime: { $avg: "$responseTime" } } },
      ]);
      const averageResponseTime =
        responseTimeResults.length > 0
          ? Math.round(responseTimeResults[0].avgResponseTime)
          : 0;

      // Get applications for different time periods
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const thisWeek = new Date();
      thisWeek.setDate(thisWeek.getDate() - 7);

      const thisMonth = new Date();
      thisMonth.setDate(thisMonth.getDate() - 30);

      const applicationsToday = await ApplicationResult.countDocuments({
        userId,
        submittedAt: { $gte: today },
      });

      const applicationsThisWeek = await ApplicationResult.countDocuments({
        userId,
        submittedAt: { $gte: thisWeek },
      });

      const applicationsThisMonth = await ApplicationResult.countDocuments({
        userId,
        submittedAt: { $gte: thisMonth },
      });

      // Get last application date
      const lastApplication = await ApplicationResult.findOne({ userId })
        .sort({ submittedAt: -1 })
        .select("submittedAt");

      return {
        totalApplications,
        successfulApplications,
        failedApplications,
        pendingApplications,
        successRate,
        averageResponseTime,
        applicationsToday,
        applicationsThisWeek,
        applicationsThisMonth,
        lastApplicationDate: lastApplication?.submittedAt || null,
      };
    } catch (error) {
      loggers.app.error("Error getting application stats:", error);
      throw error;
    }
  }

  /**
   * Get a specific application result
   * @param {string} resultId - Result ID
   * @param {string} userId - User ID (for authorization)
   * @returns {Promise<Object>} Application result
   */
  async getApplicationResult(resultId, userId) {
    try {
      const result = await ApplicationResult.findOne({ _id: resultId, userId });

      if (!result) {
        throw new Error("Application result not found");
      }

      return result;
    } catch (error) {
      loggers.app.error("Error getting application result:", error);
      throw error;
    }
  }

  /**
   * Get system health status
   * @returns {Promise<Object>} System health information
   */
  async getSystemHealth() {
    try {
      const queueSize = await ApplicationQueue.countDocuments({
        status: "pending",
      });
      const processingCount = await ApplicationQueue.countDocuments({
        status: "processing",
      });

      // Calculate error rate from last 100 applications
      const recentResults = await ApplicationResult.find({})
        .sort({ submittedAt: -1 })
        .limit(100);

      const errorRate =
        recentResults.length > 0
          ? (recentResults.filter((r) => r.status === "failed").length /
              recentResults.length) *
            100
          : 0;

      // Calculate average processing time
      const avgProcessingTime =
        recentResults.length > 0
          ? recentResults.reduce((sum, r) => sum + (r.responseTime || 0), 0) /
            recentResults.length
          : 0;

      const status =
        errorRate > 50 ? "unhealthy" : errorRate > 20 ? "degraded" : "healthy";

      return {
        status,
        components: {
          queue: queueSize > 1000 ? "degraded" : "operational",
          scraper: "operational",
          browser: "operational",
          database: "operational",
        },
        metrics: {
          queueSize,
          processingRate: processingCount,
          errorRate: Math.round(errorRate),
          averageProcessingTime: Math.round(avgProcessingTime),
        },
        lastHealthCheck: new Date(),
      };
    } catch (error) {
      loggers.app.error("Error getting system health:", error);
      return {
        status: "unhealthy",
        components: {
          queue: "down",
          scraper: "down",
          browser: "down",
          database: "down",
        },
        metrics: {
          queueSize: 0,
          processingRate: 0,
          errorRate: 100,
          averageProcessingTime: 0,
        },
        lastHealthCheck: new Date(),
      };
    }
  }

  /**
   * Process applications for a specific user with optional queue filtering
   * @param {string} userId - User ID
   * @param {Object} options - Processing options
   * @param {string} options.queueId - Optional specific queue item ID to process
   * @param {number} options.maxApplications - Maximum number of applications to process (default: 5)
   * @returns {Promise<Object>} Processing result
   */
  async processApplications(userId, options = {}) {
    try {
      const { queueId, maxApplications = 5 } = options;

      loggers.app.info(`Processing applications for user ${userId}`, {
        queueId,
        maxApplications,
      });

      // Validate user exists
      const user = await User.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Check user's auto-application settings
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings || !autoSettings.enabled) {
        throw new Error("Auto-application is not enabled for this user");
      }

      // Build query for queue items
      let query = {
        userId,
        status: "pending",
        scheduledAt: { $lte: new Date() },
      };

      // If specific queue ID is provided, filter to that item
      if (queueId) {
        query._id = queueId;
      }

      // Remove delay filter if not already passed
      const now = new Date();
      query.$or = [
        { delayUntil: { $exists: false } },
        { delayUntil: { $lte: now } },
      ];

      // Get pending applications
      const pendingApplications = await ApplicationQueue.find(query)
        .sort({ priority: -1, scheduledAt: 1 })
        .limit(Math.min(maxApplications, 10)); // Cap at 10 for safety

      if (pendingApplications.length === 0) {
        return {
          success: true,
          message: "No pending applications found to process",
          processedCount: 0,
          remainingInQueue: 0,
        };
      }

      loggers.app.info(
        `Found ${pendingApplications.length} applications to process for user ${userId}`
      );

      let processedCount = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each application
      for (const application of pendingApplications) {
        try {
          loggers.app.info(
            `Processing application ${application._id} for user ${userId}`
          );

          // Mark as processing
          application.status = "processing";
          application.processedAt = new Date();
          application.attempts += 1;
          await application.save();

          // Check rate limiting before proceeding
          const RateLimitingService = require("./rateLimitingService");
          const rateLimiter = new RateLimitingService();
          const domain = rateLimiter.extractDomain(
            application.listingUrl || application.propertyUrl
          );

          const rateLimitCheck = rateLimiter.canSubmitApplication(
            application.userId,
            domain
          );
          if (!rateLimitCheck.allowed) {
            application.status = "rate_limited";
            application.statusReason = `Rate limit: ${rateLimitCheck.reason}`;
            application.retryAfter = new Date(
              Date.now() + rateLimitCheck.retryAfter
            );
            await application.save();

            loggers.app.warn(
              `Application ${application._id} rate limited: ${rateLimitCheck.reason}`
            );
            continue; // Skip to next application
          }

          // Get user settings for form automation
          const userSettings = {
            formData: {
              firstName:
                autoSettings.personalInfo.fullName?.split(" ")[0] || "User",
              lastName:
                autoSettings.personalInfo.fullName
                  ?.split(" ")
                  .slice(1)
                  .join(" ") || "",
              email: autoSettings.personalInfo.email || user.email,
              phone: autoSettings.personalInfo.phone || "",
              age: autoSettings.personalInfo.age?.toString() || "25",
              occupation:
                autoSettings.personalInfo.occupation || "Professional",
              monthlyIncome:
                autoSettings.personalInfo.monthlyIncome?.toString() || "3000",
              additionalInfo: "Automated application via ZakMakelaar platform.",
            },
            messageTemplates: {
              default:
                application.generatedContent?.message ||
                `Dear landlord,\n\nI am interested in this property and would like to schedule a viewing.\n\nBest regards,\n${
                  autoSettings.personalInfo.fullName || user.name || user.email
                }`,
            },
          };

          // Trigger form automation engine
          const FormAutomationService = require("./formAutomationService");
          const formAutomation = new FormAutomationService();

          try {
            const result = await formAutomation.submitApplication(
              application,
              userSettings
            );

            if (result.success) {
              application.status = "completed";
              application.completedAt = new Date();
              successCount++;

              // Record successful application for rate limiting
              rateLimiter.recordApplication(application.userId, domain);

              loggers.app.info(
                `Application ${application._id} submitted successfully`
              );
            } else {
              // Manual intervention handling removed - treat all failures as regular failures
              application.status = "failed";
              application.statusReason =
                result.message || "Form automation failed";
              application.errors.push({
                message: result.message || "Form automation failed",
                category: "form",
                attempt: application.attempts,
                timestamp: new Date(),
              });
              errorCount++;
            }

            application.automationResult = result;
          } catch (formError) {
            application.status = "failed";
            application.statusReason = formError.message;
            application.errors.push({
              message: formError.message,
              category: "form",
              attempt: application.attempts,
              timestamp: new Date(),
              stack: formError.stack,
            });
            errorCount++;

            loggers.app.error(
              `Form automation failed for application ${application._id}:`,
              formError
            );
          } finally {
            await formAutomation.cleanup();
          }

          await application.save();

          // Update user statistics
          if (autoSettings) {
            await autoSettings.incrementApplicationCount();
          }

          processedCount++;
          loggers.app.info(
            `Processed application ${application._id} with status: ${application.status}`
          );
        } catch (error) {
          errorCount++;
          errors.push({
            applicationId: application._id,
            error: error.message,
          });

          // Use comprehensive error handling
          const recoveryResult = await this.errorHandlingService.handleError(
            error,
            {
              service: "AutoApplicationService",
              method: "processApplications",
              queueItemId: application._id,
              userId: application.userId,
              attemptNumber: application.attempts,
              operation: "process_application",
            }
          );

          loggers.app.error(
            `Error processing application ${application._id}:`,
            error
          );

          // If error handling didn't schedule a retry, handle it manually
          if (!recoveryResult.retryScheduled) {
            try {
              application.status = "failed";
              application.attempts += 1;
              application.errors.push({
                message: error.message,
                category: "system",
                attempt: application.attempts,
                timestamp: new Date(),
                stack: error.stack,
              });
              await application.save();
            } catch (saveError) {
              loggers.app.error(
                `Failed to save error state for application ${application._id}:`,
                saveError
              );
            }
          }
        }
      }

      // Get remaining queue count
      const remainingInQueue = await ApplicationQueue.countDocuments({
        userId,
        status: "pending",
        scheduledAt: { $lte: new Date() },
      });

      const result = {
        success: true,
        message: `Processed ${processedCount} applications (${successCount} successful, ${errorCount} failed)`,
        processedCount,
        successCount,
        errorCount,
        remainingInQueue,
        errors: errors.length > 0 ? errors : undefined,
      };

      loggers.app.info(
        `Application processing completed for user ${userId}:`,
        result
      );
      return result;
    } catch (error) {
      loggers.app.error(
        `Error in processApplications for user ${userId}:`,
        error
      );
      throw error;
    }
  }
}

module.exports = new AutoApplicationService();