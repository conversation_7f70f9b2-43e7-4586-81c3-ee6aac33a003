const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const { loggers } = require("../logger");
const {
  browserPool,
  setupPageStealth,
  autoScroll,
  getRandomDelay,
  scrapingMetrics,
} = require("../scraperUtils");
const {
  validateAndNormalizeListingEnhanced,
} = require("../transformationIntegration");

/**
 * Base scraper class that provides common functionality for all site scrapers
 */
class BaseScraper {
  constructor(siteName, config) {
    this.siteName = siteName;
    this.config = {
      maxPages: 50,
      timeout: 30000,
      retries: 3,
      delayMin: 2000,
      delayMax: 8000,
      ...config,
    };
    this.selectors = this.getSelectors();
    this.metrics = {
      startTime: null,
      pagesScraped: 0,
      listingsFound: 0,
      listingsSaved: 0,
      errors: [],
    };
  }

  /**
   * Abstract method - must be implemented by each scraper
   * @returns {Object} Site-specific CSS selectors
   */
  getSelectors() {
    throw new Error(
      `getSelectors() must be implemented by ${this.siteName} scraper`
    );
  }

  /**
   * Abstract method - must be implemented by each scraper
   * @param {Object} $ - Cheerio instance
   * @param {Object} element - DOM element
   * @returns {Object} Extracted listing data
   */
  extractListingData($, element) {
    throw new Error(
      `extractListingData() must be implemented by ${this.siteName} scraper`
    );
  }

  /**
   * Abstract method - must be implemented by each scraper
   * @returns {Array} Array of search URLs to scrape
   */
  getSearchUrls() {
    throw new Error(
      `getSearchUrls() must be implemented by ${this.siteName} scraper`
    );
  }

  /**
   * Main scraping method - common workflow for all scrapers
   */
  async scrape(retryCount = 0) {
    this.metrics.startTime = Date.now();
    loggers.scraper.info("Starting scraper", { siteName: this.siteName });

    let browser = null;
    let allListings = [];

    try {
      browser = await browserPool.getBrowser();
      const searchUrls = this.getSearchUrls();

      for (const searchUrl of searchUrls) {
        try {
          // Check if scraper implements pagination
          if (this.supportsPagination && typeof this.scrapeWithPagination === 'function') {
            const paginatedListings = await this.scrapeWithPagination(browser, searchUrl);
            allListings.push(...paginatedListings);
          } else {
            const listings = await this.scrapePage(browser, searchUrl);
            allListings.push(...listings);
          }
          this.metrics.pagesScraped++;

          // Delay between pages
          await getRandomDelay(this.config.delayMin, this.config.delayMax);
        } catch (error) {
          console.error(`Error scraping page ${searchUrl}:`, error.message);
          this.metrics.errors.push({ url: searchUrl, error: error.message });
        }
      }

      // Process and save listings
      const savedCount = await this.processListings(allListings);
      this.metrics.listingsSaved = savedCount;

      // Record success metrics
      scrapingMetrics.recordScrapeSuccess(
        this.metrics.listingsFound,
        this.metrics.listingsSaved,
        this.metrics.listingsFound - this.metrics.listingsSaved
      );

      console.log(`✅ ${this.siteName} scraping completed successfully`);
      console.log(
        `📊 Stats: ${this.metrics.pagesScraped} pages, ${this.metrics.listingsFound} found, ${this.metrics.listingsSaved} saved`
      );

      return {
        success: true,
        stats: this.metrics,
      };
    } catch (error) {
      console.error(`❌ ${this.siteName} scraping failed:`, error.message);

      // Record failure metrics
      scrapingMetrics.recordScrapeFailure(error);

      // Retry logic
      if (retryCount < this.config.retries && this.isRetryableError(error)) {
        loggers.scraper.info("Retrying scraper", {
          siteName: this.siteName,
          attempt: retryCount + 1,
          maxRetries: this.config.retries,
        });
        await getRandomDelay(5000, 10000); // Longer delay for retries
        return this.scrape(retryCount + 1);
      }

      throw error;
    } finally {
      if (browser) {
        // Close any open pages to free memory
        const pages = await browser.pages();
        for (const page of pages.slice(1)) {
          // Keep first page (about:blank)
          await page.close().catch(() => {});
        }
      }
    }
  }

  /**
   * Scrape a single page
   */
  async scrapePage(browser, url) {
    let page = null;
    try {
      page = await browser.newPage();
      await setupPageStealth(page);

      // Set site-specific configurations
      await this.configurePage(page);

      console.log(`📄 Scraping page: ${url}`);
      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: this.config.timeout,
      });

      // Wait for content to load
      await new Promise((r) => setTimeout(r, 2000));

      // Auto-scroll to load dynamic content
      await autoScroll(page);

      const html = await page.content();
      const $ = cheerio.load(html);

      // Extract listings using site-specific logic
      const listings = await this.extractListings($);
      this.metrics.listingsFound += listings.length;

      console.log(`📋 Found ${listings.length} listings on page`);
      return listings;
    } catch (error) {
      console.error(`Error scraping page ${url}:`, error.message);
      throw error;
    } finally {
      if (page) {
        await page.close().catch(() => {});
      }
    }
  }

  /**
   * Configure page with site-specific settings
   */
  async configurePage(page) {
    // Default implementation - can be overridden by subclasses
    return;
  }

  /**
   * Extract listings from page HTML
   */
  async extractListings($) {
    const listings = [];
    const listingElements = $(this.selectors.listContainer || this.selectors.listingContainer);

    for (let i = 0; i < listingElements.length; i++) {
      try {
        const element = listingElements.eq(i);
        const listingData = await this.extractListingData($, element);

        if (listingData && this.validateListing(listingData)) {
          listings.push(listingData);
        }
      } catch (error) {
        console.error(`Error extracting listing ${i}:`, error.message);
      }
    }

    return listings;
  }

  /**
   * Validate listing data
   */
  validateListing(listing) {
    return listing.title && listing.url && listing.location;
  }

  /**
   * Process and save listings to database
   */
  async processListings(listings) {
    let savedCount = 0;
    const batchSize = 10;

    for (let i = 0; i < listings.length; i += batchSize) {
      const batch = listings.slice(i, i + batchSize);

      for (const listing of batch) {
        try {
          // Normalize and validate listing
          const normalizedListing = await validateAndNormalizeListingEnhanced(
            listing
          );

          if (normalizedListing) {
            // Check for duplicates
            const existingListing = await Listing.findOne({
              url: normalizedListing.url,
            });

            if (!existingListing) {
              await Listing.create(normalizedListing);
              savedCount++;
            } else {
              console.log(
                `⚠️  Duplicate listing skipped: ${normalizedListing.url}`
              );
            }
          }
        } catch (error) {
          console.error(`Error saving listing:`, error.message);
        }
      }

      // Small delay between batches
      await new Promise((r) => setTimeout(r, 500));
    }

    return savedCount;
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    const retryableErrors = [
      "timeout",
      "network",
      "connection",
      "ECONNRESET",
      "ENOTFOUND",
      "ETIMEDOUT",
    ];

    return retryableErrors.some((keyword) =>
      error.message.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Extract text safely from element
   */
  extractText($, selector, defaultValue = null) {
    const element = $(selector);
    return element.length ? element.text().trim() : defaultValue;
  }

  /**
   * Extract attribute safely from element
   */
  extractAttribute($, selector, attribute, defaultValue = null) {
    const element = $(selector);
    return element.length ? element.attr(attribute) : defaultValue;
  }

  /**
   * Extract price using multiple strategies
   */
  extractPrice($, element) {
    const priceSelectors = Array.isArray(this.selectors.price) ? this.selectors.price : [this.selectors.price];

    // Try each price selector
    for (const selector of priceSelectors) {
      const price = this.extractText(element, selector);
      if (price && price.includes("€")) {
        return price;
      }
    }

    // Try regex patterns on element text
    const text = element.text();
    const pricePatterns = [
      /€\s*[\d.,]+\s*per\s*maand/gi,
      /€\s*[\d.,]+\s*\/\s*maand/gi,
      /€\s*[\d.,]+\s*p\.m\./gi,
      /€\s*[\d.,]+/gi,
    ];

    for (const pattern of pricePatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[0].trim();
      }
    }

    return "Prijs op aanvraag";
  }

  /**
   * Scrape with pagination support (override in subclasses that support pagination)
   */
  async scrapeWithPagination(browser, initialUrl) {
    const allListings = [];
    let currentUrl = initialUrl;
    let pageNumber = 1;
    const maxPages = this.config.maxPages || 50;

    while (currentUrl && pageNumber <= maxPages) {
      try {
        console.log(`📄 Scraping page ${pageNumber}: ${currentUrl}`);
        
        const result = await this.scrapePageWithNext(browser, currentUrl, pageNumber);
        
        if (!result.listings || result.listings.length === 0) {
          console.log(`No listings found on page ${pageNumber}, stopping pagination`);
          break;
        }

        allListings.push(...result.listings);
        this.metrics.listingsFound += result.listings.length;
        this.metrics.pagesScraped++;
        
        // Prepare next page
        currentUrl = result.nextUrl;
        pageNumber++;

        // Delay between pages
        if (currentUrl && pageNumber <= maxPages) {
          await getRandomDelay(this.config.delayMin, this.config.delayMax);
        }
      } catch (error) {
        console.error(`Error scraping page ${pageNumber}: ${currentUrl}`, error.message);
        this.metrics.errors.push({ url: currentUrl, error: error.message });
        break;
      }
    }

    console.log(`📊 Pagination completed: ${allListings.length} listings from ${pageNumber - 1} pages`);
    return allListings;
  }

  /**
   * Scrape a single page and return next page URL (override in subclasses)
   */
  async scrapePageWithNext(browser, url, pageNumber) {
    const listings = await this.scrapePage(browser, url);
    return { listings, nextUrl: null };
  }
}

module.exports = BaseScraper;
