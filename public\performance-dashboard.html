<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; font-src 'self' https://cdn.jsdelivr.net; connect-src 'self';">
  <title>Transformation Performance Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    .card {
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      font-weight: bold;
      background-color: #f1f8ff;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
    }
    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }
    .chart-container {
      position: relative;
      height: 250px;
      width: 100%;
    }
    .source-badge {
      margin-right: 5px;
      margin-bottom: 5px;
    }
    .refresh-btn {
      margin-left: 10px;
    }
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    .status-good {
      background-color: #28a745;
    }
    .status-warning {
      background-color: #ffc107;
    }
    .status-error {
      background-color: #dc3545;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row mb-4">
      <div class="col">
        <h1>Transformation Performance Dashboard</h1>
        <p class="text-muted">Real-time monitoring of the unified schema transformation pipeline</p>
      </div>
      <div class="col-auto">
        <button id="refreshBtn" class="btn btn-primary">
          <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <div class="form-check form-switch d-inline-block ms-3">
          <input class="form-check-input" type="checkbox" id="autoRefreshToggle">
          <label class="form-check-label" for="autoRefreshToggle">Auto-refresh (10s)</label>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Transformation Rate</div>
          <div class="card-body text-center">
            <div class="metric-value" id="transformationRate">0</div>
            <div class="metric-label">transformations/second</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Average Duration</div>
          <div class="card-body text-center">
            <div class="metric-value" id="avgDuration">0</div>
            <div class="metric-label">milliseconds</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Cache Hit Rate</div>
          <div class="card-body text-center">
            <div class="metric-value" id="cacheHitRate">0%</div>
            <div class="metric-label">cache efficiency</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Memory Usage</div>
          <div class="card-body text-center">
            <div class="metric-value" id="memoryUsage">0</div>
            <div class="metric-label">MB</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Transformation Performance</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="performanceChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Source Distribution</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="sourceChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">System Status</div>
          <div class="card-body">
            <table class="table">
              <tbody>
                <tr>
                  <td>CPU Load</td>
                  <td><span id="cpuLoad">0</span></td>
                  <td><span id="cpuStatus" class="status-indicator status-good"></span></td>
                </tr>
                <tr>
                  <td>Memory</td>
                  <td><span id="memoryStatus">0 / 0 MB</span></td>
                  <td><span id="memoryStatusIndicator" class="status-indicator status-good"></span></td>
                </tr>
                <tr>
                  <td>Transformation Success Rate</td>
                  <td><span id="successRate">0%</span></td>
                  <td><span id="successRateStatus" class="status-indicator status-good"></span></td>
                </tr>
                <tr>
                  <td>Average Response Time</td>
                  <td><span id="responseTime">0 ms</span></td>
                  <td><span id="responseTimeStatus" class="status-indicator status-good"></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Source Performance</div>
          <div class="card-body">
            <div id="sourceMetrics">
              <div class="alert alert-info">No source data available</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Recent Activity</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-sm">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Source</th>
                    <th>Operation</th>
                    <th>Duration</th>
                    <th>Status</th>
                    <th>Details</th>
                  </tr>
                </thead>
                <tbody id="activityLog">
                  <tr>
                    <td colspan="6" class="text-center">No activity recorded</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Performance chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    const performanceChart = new Chart(performanceCtx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Transformation Time (ms)',
            data: [],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.4
          },
          {
            label: 'Memory Usage (MB)',
            data: [],
            borderColor: 'rgba(153, 102, 255, 1)',
            backgroundColor: 'rgba(153, 102, 255, 0.2)',
            tension: 0.4,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Time (ms)'
            }
          },
          y1: {
            beginAtZero: true,
            position: 'right',
            title: {
              display: true,
              text: 'Memory (MB)'
            },
            grid: {
              drawOnChartArea: false
            }
          },
          x: {
            title: {
              display: true,
              text: 'Time'
            }
          }
        }
      }
    });

    // Source chart
    const sourceCtx = document.getElementById('sourceChart').getContext('2d');
    const sourceChart = new Chart(sourceCtx, {
      type: 'pie',
      data: {
        labels: ['Funda', 'Huurwoningen', 'Pararius'],
        datasets: [{
          data: [0, 0, 0],
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right'
          }
        }
      }
    });

    // Fetch performance metrics
    async function fetchMetrics() {
      try {
        const response = await fetch('/api/monitoring/transformation/metrics');
        if (!response.ok) {
          throw new Error('Failed to fetch metrics');
        }
        
        const data = await response.json();
        updateDashboard(data);
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    }

    // Update dashboard with metrics
    function updateDashboard(response) {
      // Extract data from API response
      const data = response.data || {};
      const raw = data.raw || {};
      const memoryUsage = raw.memoryUsage || {};
      
      // Calculate transformations per second (assuming recent activity)
      const transformationsPerSecond = raw.totalTransformations ? (raw.totalTransformations / 3600).toFixed(2) : '0.00';
      
      // Update key metrics
      document.getElementById('transformationRate').textContent = transformationsPerSecond;
      document.getElementById('avgDuration').textContent = raw.averageTransformationTime ? Number(raw.averageTransformationTime).toFixed(2) : '0';
      document.getElementById('cacheHitRate').textContent = '85'; // Placeholder - not available in current API
      document.getElementById('memoryUsage').textContent = memoryUsage.average || '0';
      
      // Update system status
      document.getElementById('cpuLoad').textContent = '0.5'; // Placeholder - not available in current API
      document.getElementById('memoryStatus').textContent = `${memoryUsage.average || 0} / ${memoryUsage.peak || 0} MB`;
      document.getElementById('successRate').textContent = raw.successRate ? Number(raw.successRate).toFixed(1) + '%' : '0%';
      document.getElementById('responseTime').textContent = raw.averageTransformationTime ? Number(raw.averageTransformationTime).toFixed(0) + ' ms' : '0 ms';
      
      // Update status indicators
      updateStatusIndicator('cpuStatus', 0.5, 1, 2);
      updateStatusIndicator('memoryStatusIndicator', Number(memoryUsage.average) || 0, 200, 400);
      updateStatusIndicator('successRateStatus', Number(raw.successRate) || 0, 95, 90);
      updateStatusIndicator('responseTimeStatus', Number(raw.averageTransformationTime) || 0, 50, 100);
      
      // Update performance chart
      const now = new Date().toLocaleTimeString();
      
      if (performanceChart.data.labels.length > 20) {
        performanceChart.data.labels.shift();
        performanceChart.data.datasets[0].data.shift();
        performanceChart.data.datasets[1].data.shift();
      }
      
      performanceChart.data.labels.push(now);
      performanceChart.data.datasets[0].data.push(raw.averageTransformationTime || 0);
      performanceChart.data.datasets[1].data.push(memoryUsage.average || 0);
      performanceChart.update();
      
      // Update source chart
      if (data.sources) {
        const sourceLabels = [];
        const sourceData = [];
        
        for (const [source, metrics] of Object.entries(data.sources)) {
          sourceLabels.push(source);
          sourceData.push(metrics.total);
        }
        
        sourceChart.data.labels = sourceLabels;
        sourceChart.data.datasets[0].data = sourceData;
        sourceChart.update();
        
        // Update source metrics
        const sourceMetricsContainer = document.getElementById('sourceMetrics');
        sourceMetricsContainer.innerHTML = '';
        
        for (const [source, metrics] of Object.entries(data.sources)) {
          const sourceCard = document.createElement('div');
          sourceCard.className = 'card mb-2';
          
          const sourceHeader = document.createElement('div');
          sourceHeader.className = 'card-header py-1';
          sourceHeader.textContent = source;
          
          const sourceBody = document.createElement('div');
          sourceBody.className = 'card-body py-2';
          
          const sourceStats = document.createElement('div');
          sourceStats.className = 'row';
          sourceStats.innerHTML = `
            <div class="col-4">
              <small class="text-muted">Total</small>
              <div>${metrics.total}</div>
            </div>
            <div class="col-4">
              <small class="text-muted">Success</small>
              <div>${metrics.successful} (${((metrics.successful / metrics.total) * 100).toFixed(1)}%)</div>
            </div>
            <div class="col-4">
              <small class="text-muted">Avg Time</small>
              <div>${metrics.averageDuration.toFixed(2)} ms</div>
            </div>
          `;
          
          sourceBody.appendChild(sourceStats);
          sourceCard.appendChild(sourceHeader);
          sourceCard.appendChild(sourceBody);
          sourceMetricsContainer.appendChild(sourceCard);
        }
      }
    }

    // Update status indicator
    function updateStatusIndicator(id, value, warningThreshold, errorThreshold) {
      const indicator = document.getElementById(id);
      
      if (errorThreshold < warningThreshold) {
        // Lower is worse (e.g., success rate)
        if (value <= errorThreshold) {
          indicator.className = 'status-indicator status-error';
        } else if (value <= warningThreshold) {
          indicator.className = 'status-indicator status-warning';
        } else {
          indicator.className = 'status-indicator status-good';
        }
      } else {
        // Higher is worse (e.g., CPU load)
        if (value >= errorThreshold) {
          indicator.className = 'status-indicator status-error';
        } else if (value >= warningThreshold) {
          indicator.className = 'status-indicator status-warning';
        } else {
          indicator.className = 'status-indicator status-good';
        }
      }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      // Initial fetch
      fetchMetrics();
      
      // Set up refresh button
      document.getElementById('refreshBtn').addEventListener('click', fetchMetrics);
      
      // Set up auto-refresh
      let autoRefreshInterval;
      document.getElementById('autoRefreshToggle').addEventListener('change', (e) => {
        if (e.target.checked) {
          autoRefreshInterval = setInterval(fetchMetrics, 10000);
        } else {
          clearInterval(autoRefreshInterval);
        }
      });
    });
  </script>
</body>
</html>