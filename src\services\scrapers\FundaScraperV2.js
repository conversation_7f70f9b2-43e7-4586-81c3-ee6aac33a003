const cheerio = require('cheerio');
const BaseScraper = require('./BaseScraper');
const { getScraperConfig } = require('./configs/scraperConfigs');
const { ErrorClassifier, ErrorRecovery, errorMetrics } = require('./errors/ScrapingErrors');
const { rateLimiter, antiDetection } = require('./utils/RateLimiter');

/**
 * Enhanced Funda scraper using the new architecture
 */
class FundaScraper extends BaseScraper {
  constructor() {
    const config = getScraperConfig('funda');
    super('funda', config);
    this.supportsPagination = true;
  }

  /**
   * Clean extracted title
   */
  cleanTitle(title) {
    if (!title) return null;
    return title.trim();
  }

  /**
   * Clean extracted location
   */
  cleanLocation(location) {
    if (!location) return null;
    return location.trim();
  }

  /**
   * Get site-specific selectors
   */
  getSelectors() {
    return this.config.selectors;
  }

  /**
   * Generate search URLs based on configuration
   */
  getSearchUrls() {
    // Use Netherlands-wide search to get all available rental listings
    return ['https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22'];
  }

  /**
   * Configure page with site-specific settings
   */
  async configurePage(page) {
    // Apply anti-detection measures
    await antiDetection.setupAntiDetection(page, this.siteName);

    // Set Funda-specific headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'nl-NL,nl;q=0.9,en;q=0.8',
      'Referer': 'https://www.funda.nl/'
    });

    // Allow all resources to load properly
    // Note: Resource blocking disabled to ensure page content loads
  }

  /**
   * Extract listing data from a single listing element
   */
  async extractListingData($, element) {
    try {
      const listing = {
        source: 'funda',
        scrapedAt: new Date()
      };

      // Extract basic information
      listing.title = this.extractText(element, this.selectors.title);
      listing.location = this.extractText(element, this.selectors.location);
      listing.price = this.extractPrice($, element);
      
      // Extract URL - use 'url' selector from config
      const urlElement = element.find(this.selectors.url);
      if (urlElement.length) {
        const relativeUrl = urlElement.attr('href');
        listing.url = relativeUrl ? (relativeUrl.startsWith('http') ? relativeUrl : `${this.config.baseUrl}${relativeUrl}`) : null;
      }

      // Extract image
      const imageElement = element.find(this.selectors.image);
      if (imageElement.length) {
        listing.image = imageElement.attr('src') || imageElement.attr('data-src');
      }

      // Extract size and rooms from listing preview
      listing.size = this.extractText(element, this.selectors.size);
      listing.rooms = this.extractText(element, this.selectors.rooms);

      // If we have a detail URL, fetch additional details
      if (listing.url && this.shouldFetchDetails()) {
        try {
          await rateLimiter.waitForDelay(this.siteName);
          const detailData = await this.fetchListingDetails(listing.url);
          Object.assign(listing, detailData);
        } catch (error) {
          console.warn(`Failed to fetch details for ${listing.url}:`, error.message);
          // Continue with basic data
        }
      }

      return this.validateListing(listing) ? listing : null;

    } catch (error) {
      console.error('Error extracting Funda listing:', error.message);
      return null;
    }
  }

  /**
   * Fetch detailed information from listing page
   */
  async fetchListingDetails(url) {
    const browser = await this.getBrowserInstance();
    let page = null;

    try {
      page = await browser.newPage();
      await this.configurePage(page);

      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: this.config.timeout,
      });

      // Add human-like behavior
      await antiDetection.addHumanBehavior(page);

      const html = await page.content();
      const $ = cheerio.load(html);

      const details = {};

      // Extract detailed information using configured selectors
      const { detailSelectors } = this.selectors;

      // Description
      details.description = this.extractText($, detailSelectors.description);
      if (details.description) {
        details.description = details.description.substring(0, 1000); // Limit length
      }

      // Property features
      details.energyLabel = this.extractText($, detailSelectors.energyLabel);
      details.availableFrom = this.extractText($, detailSelectors.availableFrom);
      details.propertyType = this.extractText($, detailSelectors.propertyType) || 'woning';
      details.interior = this.extractText($, detailSelectors.interior);
      details.garden = this.extractText($, detailSelectors.garden);
      details.parking = this.extractText($, detailSelectors.parking);

      // Extract features from feature list
      $(detailSelectors.features).each((i, el) => {
        const label = $(el).text().trim().toLowerCase();
        const valueEl = $(el).next('dd');
        const value = valueEl.length ? valueEl.text().trim() : null;

        if (value) {
          if (label.includes('woonoppervlakte')) {
            details.size = value;
          } else if (label.includes('kamers')) {
            details.rooms = value;
          } else if (label.includes('slaapkamers')) {
            details.bedrooms = value;
          } else if (label.includes('bouwjaar')) {
            details.year = value;
          }
        }
      });

      // Extract images
      details.images = [];
      $('img[src*="cloud.funda.nl"], img[data-src*="cloud.funda.nl"]').each((i, img) => {
        const src = $(img).attr('src') || $(img).attr('data-src');
        if (src && !details.images.includes(src)) {
          details.images.push(src);
        }
      });

      // Try to extract price from JSON-LD if not found
      if (!details.price || details.price === "Prijs op aanvraag") {
        const jsonLdScripts = $('script[type="application/ld+json"]');
        jsonLdScripts.each((i, script) => {
          try {
            const jsonData = JSON.parse($(script).html());
            if (jsonData.offers && jsonData.offers.price) {
              details.price = `€ ${jsonData.offers.price}`;
            }
          } catch (e) {
            // Ignore JSON parsing errors
          }
        });
      }

      return details;

    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      console.error(`Error fetching Funda details from ${url}:`, error.message);
      throw classifiedError;
    } finally {
      if (page) {
        await page.close().catch(() => {});
      }
    }
  }

  /**
   * Enhanced scraping with error handling and recovery
   */
  async scrape(retryCount = 0) {
    try {
      // Apply rate limiting before starting
      await rateLimiter.waitForDelay(this.siteName);
      
      return await super.scrape(retryCount);
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      // Apply error recovery strategy
      const recovery = await ErrorRecovery.handleError(classifiedError, {
        siteName: this.siteName,
        retryCount,
        maxRetries: this.config.retries
      });

      if (recovery.shouldRetry && retryCount < this.config.retries) {
        console.log(`🔄 Applying recovery strategy for ${this.siteName}`);
        
        // Apply recovery strategies
        if (recovery.increaseTimeout) {
          this.config.timeout = Math.min(this.config.timeout * 1.5, 60000);
        }
        
        if (recovery.reduceSpeed) {
          this.config.delayMin *= 1.5;
          this.config.delayMax *= 1.5;
        }

        // Record error for rate limiter
        rateLimiter.recordError(this.siteName, classifiedError);

        return this.scrape(retryCount + 1);
      }

      throw classifiedError;
    }
  }

  /**
   * Validate Funda-specific listing data
   */
  validateListing(listing) {
    if (!super.validateListing(listing)) {
      return false;
    }

    // Funda-specific validations
    if (!listing.url || !listing.url.includes('funda.nl')) {
      return false;
    }

    // Ensure price is reasonable (basic sanity check)
    if (listing.price && listing.price !== "Prijs op aanvraag") {
      const priceMatch = listing.price.match(/[\d.,]+/);
      if (priceMatch) {
        const numericPrice = parseFloat(priceMatch[0].replace(/[.,]/g, ''));
        if (numericPrice < 100 || numericPrice > 50000) { // Monthly rent range check
          console.warn(`Suspicious price for Funda listing: ${listing.price}`);
        }
      }
    }

    return true;
  }

  /**
   * Determine if we should fetch detailed information
   */
  shouldFetchDetails() {
    // Fetch details for every 3rd listing to balance completeness vs speed
    return Math.random() < 0.33;
  }

  /**
   * Get browser instance (reuse from pool)
   */
  async getBrowserInstance() {
    const { browserPool } = require('../scraperUtils');
    return browserPool.getBrowser();
  }

  /**
   * Override scrapePageWithNext to support pagination
   */
  async scrapePageWithNext(browser, url, pageNumber) {
    let page = null;
    try {
      // Apply rate limiting before each page
      await rateLimiter.waitForDelay(this.siteName);
      
      page = await browser.newPage();
      await this.configurePage(page);

      console.log(`📄 Scraping Funda page ${pageNumber}: ${url}`);
      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: this.config.timeout,
      });

      // Wait for content to load
      await new Promise((r) => setTimeout(r, 2000));

      const html = await page.content();
      const $ = cheerio.load(html);

      // Extract listings using site-specific logic
      const listings = await this.extractListings($);
      
      // Find next page URL
      let nextUrl = null;
      const nextHref =
        $('a[rel="next"]').attr('href') ||
        $('a[aria-label="Volgende"]').attr('href') ||
        $('a[aria-label="Next"]').attr('href') ||
        $('nav a:contains("Volgende")').attr('href') ||
        $('nav a:contains("Next")').attr('href');
      if (nextHref) {
        nextUrl = nextHref.startsWith('http') ? nextHref : `https://www.funda.nl${nextHref}`;
      }

      console.log(`📋 Found ${listings.length} listings on page ${pageNumber}`);
      return { listings, nextUrl };
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      rateLimiter.recordError(this.siteName, classifiedError);
      throw classifiedError;
    } finally {
      if (page) {
        await page.close().catch(() => {});
      }
    }
  }
}

// Export both class and factory function
const createFundaScraper = () => new FundaScraper();

module.exports = {
  FundaScraper,
  createFundaScraper,
  // Export default scraping function for backward compatibility
  scrapeFunda: async () => {
    const scraper = new FundaScraper();
    return scraper.scrape();
  }
};
