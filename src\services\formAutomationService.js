const puppeteer = require("puppeteer");
const { loggers } = require("./logger");
const AutoApplicationSettings = require("../models/AutoApplicationSettings");
const { setupPageStealth } = require("./scraperUtils");
const AntiDetectionSystem = require("./antiDetectionSystem");
const path = require("path");
const fs = require("fs");

class FormAutomationService {
  constructor() {
    this.browser = null;
    this.isInitialized = false;
    this.screenshotDir = path.join(process.cwd(), "screenshots");
    this.antiDetection = new AntiDetectionSystem();
    this.ensureScreenshotDir();
  }

  // Ensure screenshot directory exists
  ensureScreenshotDir() {
    try {
      if (!fs.existsSync(this.screenshotDir)) {
        fs.mkdirSync(this.screenshotDir, { recursive: true });
        loggers.app.info(`Created screenshot directory: ${this.screenshotDir}`);
      }
    } catch (error) {
      loggers.app.error("Failed to create screenshot directory:", error);
    }
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Determine the best Chrome executable path
      let executablePath = null;

      // Check if we're in Docker or have environment variable set
      if (process.env.PUPPETEER_EXECUTABLE_PATH) {
        executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
      } else {
        // Try different Chrome paths
        const fs = require("fs");
        const possiblePaths = [
          puppeteer.executablePath(),
          "/usr/bin/google-chrome-stable",
          "/usr/bin/google-chrome",
          "/usr/bin/chromium-browser",
          "/usr/bin/chromium",
          "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
          "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
        ];

        for (const path of possiblePaths) {
          try {
            if (fs.existsSync(path)) {
              executablePath = path;
              break;
            }
          } catch (e) {
            // Continue to next path
          }
        }
      }

      if (!executablePath) {
        throw new Error(
          "Chrome executable not found. Install Chrome or set PUPPETEER_EXECUTABLE_PATH"
        );
      }

      // Demo mode configuration
      const isDemoMode = process.env.DEMO_MODE === "true";
      const headlessMode = isDemoMode
        ? process.env.DEMO_BROWSER_HEADLESS === "true"
        : process.env.FUNDA_AUTO_APPLICATION_HEADLESS === "true";

      const launchOptions = {
        executablePath: executablePath,
        headless: headlessMode,
        devtools: isDemoMode && process.env.DEMO_SHOW_BROWSER === "true",
        slowMo: isDemoMode
          ? parseInt(process.env.DEMO_SLOW_MOTION || "100")
          : 0,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--disable-gpu",
          "--disable-web-security",
          "--allow-running-insecure-content",
          "--disable-features=VizDisplayCompositor",
          // Enable CSS loading and rendering
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--enable-features=NetworkService,NetworkServiceLogging",
          // Demo-specific positioning
          ...(isDemoMode
            ? ["--window-position=100,100", "--window-size=1200,800"]
            : []),
        ],
      };

      // Add Docker-specific args if we detect we're in a container
      const fs = require("fs");
      if (
        process.env.PUPPETEER_EXECUTABLE_PATH ||
        fs.existsSync("/.dockerenv")
      ) {
        launchOptions.args.push(
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding"
        );
      }

      this.browser = await puppeteer.launch(launchOptions);
      this.isInitialized = true;

      const modeDescription = isDemoMode ? "DEMO MODE" : "PRODUCTION MODE";
      const visibilityDescription = headlessMode ? "HEADLESS" : "VISIBLE";

      loggers.app.info(
        `Form automation service initialized in ${modeDescription} (${visibilityDescription})`
      );
    } catch (error) {
      loggers.app.error("Failed to initialize form automation service:", error);
      throw error;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
    }
  }

  _calculateAge(dateOfBirth) {
    if (!dateOfBirth) return null;
    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  }

  // Small delay helper (compatible with Puppeteer v24+)
  sleep(ms) {
    return new Promise(function (resolve) {
      setTimeout(resolve, ms);
    });
  }

  // Demo-specific method for highlighting elements
  async highlightElement(page, selector, applicationId) {
    if (process.env.DEMO_MODE !== "true") return;

    try {
      await page.evaluate((sel) => {
        const element = document.querySelector(sel);
        if (element) {
          element.style.border = "3px solid #ff0000";
          element.style.backgroundColor = "rgba(255, 255, 0, 0.3)";
          setTimeout(() => {
            element.style.border = "";
            element.style.backgroundColor = "";
          }, 2000);
        }
      }, selector);

      await this.sleep(1000); // Give time to see the highlight

      loggers.app.debug("Element highlighted for demo", {
        applicationId,
        selector,
      });
    } catch (error) {
      loggers.app.debug("Failed to highlight element:", {
        error: error.message,
        selector,
        applicationId,
      });
    }
  }

  // Wait for page to render completely including CSS
  async waitForPageToRender(page, applicationId) {
    try {
      loggers.app.debug("Waiting for page to render completely", {
        applicationId,
      });

      // Wait for DOM to be ready
      await page.waitForFunction(() => document.readyState === "complete", {
        timeout: 30000,
      });

      // Wait for CSS to load by checking for styled elements
      await page
        .waitForFunction(
          () => {
            const body = document.body;
            if (!body) return false;

            const computedStyle = window.getComputedStyle(body);
            // Check if basic styling is applied (fonts, colors, etc.)
            return (
              computedStyle.fontFamily !== "" ||
              computedStyle.backgroundColor !== "rgba(0, 0, 0, 0)" ||
              computedStyle.color !== "rgb(0, 0, 0)"
            );
          },
          { timeout: 15000 }
        )
        .catch(() => {
          loggers.app.warn("CSS loading timeout, proceeding anyway", {
            applicationId,
          });
        });

      // Additional wait for dynamic content
      await this.sleep(2000);

      // Check if page loaded without errors
      const hasContent = await page.evaluate(() => {
        return document.body && document.body.children.length > 0;
      });

      if (!hasContent) {
        throw new Error("Page appears to be empty or failed to load properly");
      }

      // Check for error pages or blocked access
      const pageText = await page.evaluate(
        () => document.body.textContent || ""
      );
      const errorKeywords = [
        "403",
        "404",
        "access denied",
        "blocked",
        "error",
        "not found",
      ];
      const hasError = errorKeywords.some((keyword) =>
        pageText.toLowerCase().includes(keyword)
      );

      if (hasError) {
        loggers.app.warn("Potential error page detected", {
          applicationId,
          pageText: pageText.slice(0, 200),
        });
      }

      loggers.app.debug("Page rendering check completed", {
        applicationId,
        hasContent,
        hasError,
      });
    } catch (error) {
      loggers.app.error("Error waiting for page to render", {
        applicationId,
        error: error.message,
      });
      throw error;
    }
  }

  // Check for confirmation message or email after form submission
  async checkForConfirmation(page, applicationId, platform) {
    try {
      loggers.app.debug("Checking for confirmation message", {
        applicationId,
        platform,
        url: page.url(),
      });

      // Wait a bit for any confirmation to appear
      await this.sleep(3000);

      const formAutomationConfig = require("../config/formAutomationConfig");
      const platformConfig = formAutomationConfig.platforms[platform];

      if (!platformConfig || !platformConfig.selectors) {
        loggers.app.warn(
          "No platform configuration found for confirmation check",
          {
            applicationId,
            platform,
          }
        );
        return { found: false, message: "No platform configuration" };
      }

      // Check for success/confirmation messages
      const confirmationSelectors = [
        platformConfig.selectors.confirmationMessage,
        platformConfig.selectors.confirmationEmail,
        ".success",
        ".confirmation",
        ".thank-you",
        '[data-testid="success"]',
        '[data-testid="confirmation"]',
        'div:contains("bedankt")',
        'div:contains("verzonden")',
        'div:contains("ontvangen")',
        'div:contains("thank you")',
      ].filter(Boolean);

      for (const selector of confirmationSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const text = await page.evaluate((el) => el.textContent, element);

            if (process.env.DEMO_MODE === "true") {
              await this.highlightElement(page, selector, applicationId);
            }

            loggers.app.info("Confirmation message found", {
              applicationId,
              platform,
              selector,
              text: text.slice(0, 100),
            });

            return {
              found: true,
              selector,
              message: text,
              type: "confirmation_message",
            };
          }
        } catch (error) {
          // Continue checking other selectors
          loggers.app.debug("Selector check failed:", {
            selector,
            error: error.message,
            applicationId,
          });
        }
      }

      // Check for error messages that might indicate issues
      const errorSelectors = [
        platformConfig.selectors.errorMessage,
        ".error",
        ".alert-error",
        '[role="alert"]',
        'div:contains("fout")',
        'div:contains("error")',
      ].filter(Boolean);

      for (const selector of errorSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const text = await page.evaluate((el) => el.textContent, element);

            loggers.app.warn("Error message found on page", {
              applicationId,
              platform,
              selector,
              text: text.slice(0, 100),
            });

            return {
              found: true,
              selector,
              message: text,
              type: "error_message",
            };
          }
        } catch (error) {
          // Continue checking
        }
      }

      // If we're still on the same page, might be a redirect issue
      const currentUrl = page.url();
      loggers.app.debug("No confirmation message found", {
        applicationId,
        platform,
        currentUrl,
        selectorsChecked: confirmationSelectors.length,
      });

      return {
        found: false,
        message: "No confirmation message detected",
        currentUrl,
      };
    } catch (error) {
      loggers.app.error("Error checking for confirmation", {
        error: error.message,
        applicationId,
        platform,
      });

      return {
        found: false,
        error: error.message,
      };
    }
  }

  // Add human-like behavior to avoid detection
  async addHumanLikeBehavior(page, context = {}) {
    const { applicationId } = context;
    loggers.app.debug("Adding human-like behavior to page", {
      applicationId,
      pageUrl: page.url(),
    });

    try {
      // Use the comprehensive anti-detection system for better human simulation
      loggers.app.debug(
        "Using anti-detection system for human behavior simulation",
        { applicationId }
      );
      await this.antiDetection.simulateHumanBehavior(page, {
        mouseMovements: true,
        scrolling: true,
        typing: false, // We'll handle typing separately
        delays: true,
      });
      loggers.app.debug(
        "Anti-detection human behavior simulation completed successfully",
        { applicationId }
      );
    } catch (error) {
      // Fallback to basic behavior if anti-detection fails
      loggers.app.debug("Anti-detection behavior failed, using fallback:", {
        error: error.message,
        applicationId,
      });

      try {
        // Random mouse movements
        const viewport = await page.viewport();
        const randomX = Math.random() * viewport.width;
        const randomY = Math.random() * viewport.height;

        loggers.app.debug("Performing fallback mouse movement", {
          applicationId,
          fromX: 0,
          fromY: 0,
          toX: Math.round(randomX),
          toY: Math.round(randomY),
          steps: 5,
        });

        await page.mouse.move(randomX, randomY, { steps: 5 });
        const mouseDelay = Math.random() * 200 + 100;
        loggers.app.debug("Mouse movement completed, adding delay", {
          applicationId,
          delayMs: Math.round(mouseDelay),
        });
        await this.sleep(mouseDelay);

        // Random scroll
        const scrollAmount = Math.random() * 300 - 150; // -150 to +150
        loggers.app.debug("Performing random scroll", {
          applicationId,
          scrollAmount: Math.round(scrollAmount),
        });

        await page.evaluate((amount) => {
          window.scrollBy(0, amount);
        }, scrollAmount);

        const scrollDelay = Math.random() * 300 + 200;
        loggers.app.debug("Scroll completed, adding delay", {
          applicationId,
          delayMs: Math.round(scrollDelay),
        });
        await this.sleep(scrollDelay);

        // Sometimes move mouse again
        if (Math.random() > 0.5) {
          const randomX2 = Math.random() * viewport.width;
          const randomY2 = Math.random() * viewport.height;

          loggers.app.debug("Performing additional mouse movement", {
            applicationId,
            toX: Math.round(randomX2),
            toY: Math.round(randomY2),
            steps: 3,
          });

          await page.mouse.move(randomX2, randomY2, { steps: 3 });
        }

        const finalDelay = Math.random() * 200 + 100;
        loggers.app.debug("Human behavior simulation completed, final delay", {
          applicationId,
          delayMs: Math.round(finalDelay),
        });
        await this.sleep(finalDelay);

        loggers.app.debug(
          "Fallback human behavior simulation completed successfully",
          { applicationId }
        );
      } catch (fallbackError) {
        loggers.app.warn("Fallback human behavior simulation also failed", {
          applicationId,
          error: fallbackError.message,
        });
      }
    }
  }

  // Launch browser for testing purposes
  async launchBrowser() {
    // Determine the best Chrome executable path
    let executablePath = null;

    // Check if we're in Docker or have environment variable set
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
    } else {
      // Try different Chrome paths
      const fs = require("fs");
      const possiblePaths = [
        puppeteer.executablePath(),
        "/usr/bin/google-chrome-stable",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
      ];

      for (const path of possiblePaths) {
        try {
          if (fs.existsSync(path)) {
            executablePath = path;
            break;
          }
        } catch (e) {
          // Continue to next path
        }
      }
    }

    if (!executablePath) {
      throw new Error(
        "Chrome executable not found. Install Chrome or set PUPPETEER_EXECUTABLE_PATH"
      );
    }

    const launchOptions = {
      executablePath: executablePath,
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--disable-gpu",
      ],
    };

    // Add Docker-specific args if we detect we're in a container
    const fs = require("fs");
    if (process.env.PUPPETEER_EXECUTABLE_PATH || fs.existsSync("/.dockerenv")) {
      launchOptions.args.push(
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding"
      );
    }

    return await puppeteer.launch(launchOptions);
  }

  // Take screenshot with descriptive filename
  async takeScreenshot(page, application, step, additionalInfo = "") {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const domain = new URL(application.propertyUrl).hostname;
      const filename = `${timestamp}_${application._id}_${domain}_${step}${ 
        additionalInfo ? "_" + additionalInfo : ""
      }.png`;
      const filepath = path.join(this.screenshotDir, filename);

      await page.screenshot({
        path: filepath,
        fullPage: true,
        type: "png",
      });

      loggers.app.info(`Screenshot saved: ${filename}`);
      return filepath;
    } catch (error) {
      loggers.app.error("Failed to take screenshot:", error);
      return null;
    }
  }

  // Take screenshot on error with additional context
  async takeErrorScreenshot(page, application, error, step) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const domain = new URL(application.propertyUrl).hostname;
      const filename = `ERROR_${timestamp}_${application._id}_${domain}_${step}.png`;
      const filepath = path.join(this.screenshotDir, filename);

      await page.screenshot({
        path: filepath,
        fullPage: true,
        type: "png",
      });

      // Also save page HTML for debugging
      const htmlFilename = `ERROR_${timestamp}_${application._id}_${domain}_${step}.html`;
      const htmlFilepath = path.join(this.screenshotDir, htmlFilename);
      const html = await page.content();
      fs.writeFileSync(htmlFilepath, html);

      loggers.app.error(
        `Error screenshot and HTML saved: ${filename}, ${htmlFilename}`,
        {
          error: error.message,
          step: step,
          url: application.propertyUrl,
        }
      );

      return { screenshot: filepath, html: htmlFilepath };
    } catch (screenshotError) {
      loggers.app.error("Failed to take error screenshot:", screenshotError);
      return null;
    }
  }

  async submitApplication(application, providedUserSettings = null) {
    loggers.app.debug("Starting application submission process", {
      applicationId: application._id,
      userId: application.userId,
      propertyUrl: application.propertyUrl,
      hasProvidedSettings: !!providedUserSettings,
    });

    // Enhanced demo mode logging
    if (process.env.DEMO_MODE === "true") {
      loggers.app.info(
        "🎬 DEMO MODE: Starting form automation with visible browser"
      );
      loggers.app.info("📋 Demo Configuration:", {
        DEMO_MODE: process.env.DEMO_MODE,
        DEMO_BROWSER_HEADLESS: process.env.DEMO_BROWSER_HEADLESS,
        DEMO_SHOW_BROWSER: process.env.DEMO_SHOW_BROWSER,
        FUNDA_AUTO_APPLICATION_HEADLESS:
          process.env.FUNDA_AUTO_APPLICATION_HEADLESS,
        DEMO_SLOW_MOTION: process.env.DEMO_SLOW_MOTION,
      });
    }

    if (!this.isInitialized) {
      loggers.app.debug(
        "Form automation service not initialized, initializing now"
      );
      await this.initialize();
    }

    const page = await this.browser.newPage();
    loggers.app.debug("New browser page created", {
      applicationId: application._id,
      pageUrl: "about:blank",
    });

    // Set page configurations for better resource loading
    await page.setDefaultNavigationTimeout(60000);
    await page.setDefaultTimeout(30000);

    // Enable CSS and image loading
    await page.setRequestInterception(false);

    // Ensure viewport is set before navigation
    await page.setViewport({ width: 1366, height: 768 });

    // Apply comprehensive anti-detection measures
    try {
      loggers.app.debug("Applying anti-detection measures", {
        applicationId: application._id,
      });
      await this.antiDetection.randomizeFingerprint(page);
      await this.antiDetection.simulateHumanBehavior(page, {
        mouseMovements: true,
        scrolling: true,
        delays: true,
      });
      loggers.app.info("Applied comprehensive anti-detection measures");
      loggers.app.debug("Anti-detection measures applied successfully", {
        applicationId: application._id,
      });
    } catch (e) {
      loggers.app.warn(
        "Failed to apply anti-detection measures, falling back to basic stealth",
        {
          error: e?.message,
          applicationId: application._id,
        }
      );
      // Fallback to basic stealth
      try {
        await setupPageStealth(page);
        loggers.app.info("Applied basic stealth settings");
        loggers.app.debug("Basic stealth settings applied successfully", {
          applicationId: application._id,
        });
      } catch (fallbackError) {
        loggers.app.warn("Basic stealth also failed", {
          error: fallbackError?.message,
          applicationId: application._id,
        });
      }
    }

    try {
      // Extra HTTP headers to look more like a real browser
      loggers.app.debug("Setting HTTP headers", {
        applicationId: application._id,
      });
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
      });

      // Set user agent to avoid detection
      const userAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
      await page.setUserAgent(userAgent);
      loggers.app.debug("User agent set", {
        applicationId: application._id,
        userAgent,
      });

      // Add realistic session cookies to appear as a returning user
      try {
        loggers.app.debug("Setting session cookies", {
          applicationId: application._id,
        });
        await page.setCookie(
          {
            name: "OptanonAlertBoxClosed",
            value: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            domain: ".funda.nl",
            httpOnly: false,
            secure: true,
          },
          {
            name: "OptanonConsent",
            value:
              "isGpcEnabled=0&datestamp=" +
              new Date(Date.now() - 86400000).toISOString() +
              "&version=6.26.0",
            domain: ".funda.nl",
            httpOnly: false,
            secure: true,
          },
          {
            name: "ajs_anonymous_id",
            value: "%22" + Math.random().toString(36).substring(2, 15) + "%22",
            domain: ".funda.nl",
            httpOnly: false,
            secure: true,
          },
          {
            name: "visitor_id",
            value:
              Math.random().toString(36).substring(2, 15) +
              Date.now().toString(36),
            domain: ".funda.nl",
            httpOnly: false,
            secure: true,
          }
        );
        loggers.app.info("Set realistic session cookies");
        loggers.app.debug("Session cookies set successfully", {
          applicationId: application._id,
        });
      } catch (cookieError) {
        loggers.app.warn("Failed to set session cookies", {
          error: cookieError.message,
          applicationId: application._id,
        });
      }

      // Take initial screenshot
      loggers.app.debug("Taking initial screenshot", {
        applicationId: application._id,
      });
      await this.takeScreenshot(page, application, "01_initial");

      // Get user settings for form data
      let userSettings;
      loggers.app.debug("Processing user settings", {
        applicationId: application._id,
        hasProvidedSettings: !!providedUserSettings,
      });

      if (providedUserSettings) {
        // Use provided userSettings (already in correct format)
        userSettings = providedUserSettings;
        loggers.app.info("Using provided userSettings");
        loggers.app.debug("Using provided user settings", {
          applicationId: application._id,
          hasFormData: !!userSettings.formData,
          formDataKeys: userSettings.formData
            ? Object.keys(userSettings.formData)
            : [],
        });
      } else {
        // Fetch from database and transform to expected format
        loggers.app.debug("Fetching user settings from database", {
          applicationId: application._id,
          userId: application.userId,
        });

        const autoSettings = await AutoApplicationSettings.findByUserId(
          application.userId
        );
        if (!autoSettings) {
          const error = new Error("User auto-application settings not found");
          loggers.app.debug("Auto-application settings not found", {
            applicationId: application._id,
            userId: application.userId,
          });
          throw error;
        }

        // Transform AutoApplicationSettings to expected format
        loggers.app.debug(
          "Transforming auto-application settings to user settings format",
          {
            applicationId: application._id,
            userId: application.userId,
            hasPersonalInfo: !!autoSettings.personalInfo,
          }
        );

        const age = this._calculateAge(autoSettings.personalInfo.dateOfBirth) || autoSettings.personalInfo.age;

        userSettings = {
          formData: {
            firstName:
              autoSettings.personalInfo.fullName?.split(" ")[0] || "User",
            lastName:
              autoSettings.personalInfo.fullName
                ?.split(" ")
                .slice(1)
                .join(" ") || "",
            email: autoSettings.personalInfo.email || "",
            phone: autoSettings.personalInfo.phone || "",
            age: age?.toString() || "25",
            occupation: autoSettings.personalInfo.occupation || "Professional",
            monthlyIncome:
              autoSettings.personalInfo.monthlyIncome?.toString() || "3000",
            additionalInfo: "Automated application via ZakMakelaar platform.",
          },
          messageTemplates: {
            default: `Dear landlord,\n\nI am interested in this property and would like to schedule a viewing.\n\nBest regards,\n${ 
              autoSettings.personalInfo.fullName || "Applicant"
            }`,
          },
        };
        loggers.app.info(
          "Transformed AutoApplicationSettings to userSettings format"
        );
        loggers.app.debug("User settings transformation completed", {
          applicationId: application._id,
          transformedFormDataKeys: Object.keys(userSettings.formData),
        });
      }

      // Debug: Log userSettings structure
      loggers.app.info("DEBUG - Final userSettings structure:", {
        hasUserSettings: !!userSettings,
        hasFormData: !!(userSettings && userSettings.formData),
        formDataKeys:
          userSettings && userSettings.formData
            ? Object.keys(userSettings.formData)
            : "N/A",
      });

      // Navigate to property page
      loggers.app.info(`🌐 Navigating to: ${application.propertyUrl}`);
      loggers.app.debug("Starting navigation to property page", {
        applicationId: application._id,
        url: application.propertyUrl,
        urlType: typeof application.propertyUrl,
        urlLength: application.propertyUrl?.length,
        isValidUrl: application.propertyUrl?.startsWith("http"),
      });

      // Check current page URL before navigation
      const currentUrl = page.url();
      loggers.app.debug("Current page URL before navigation", {
        applicationId: application._id,
        currentUrl: currentUrl,
      });

      // Add a realistic delay before navigation to simulate user behavior
      const preNavigationDelay = Math.random() * 2000 + 1000; // 1-3 seconds
      loggers.app.debug("Adding pre-navigation delay", {
        applicationId: application._id,
        delayMs: Math.round(preNavigationDelay),
      });
      await this.sleep(preNavigationDelay);

      // Enhanced navigation with detailed error handling
      try {
        loggers.app.info("🚀 Starting page.goto() navigation...");
        await page.goto(application.propertyUrl, {
          waitUntil: "networkidle0",
          timeout: 60000,
        });

        // Verify navigation was successful
        const finalUrl = page.url();
        loggers.app.info(
          `✅ Navigation completed successfully to: ${finalUrl}`
        );
        loggers.app.debug("Navigation verification", {
          applicationId: application._id,
          requestedUrl: application.propertyUrl,
          finalUrl: finalUrl,
          navigationSuccessful:
            finalUrl !== "about:blank" && finalUrl.includes("funda.nl"),
        });

        if (finalUrl === "about:blank") {
          throw new Error(
            `Navigation failed: still on about:blank after attempting to navigate to ${application.propertyUrl}`
          );
        }
      } catch (navigationError) {
        loggers.app.error("❌ Navigation failed", {
          applicationId: application._id,
          error: navigationError.message,
          targetUrl: application.propertyUrl,
          currentPageUrl: page.url(),
        });

        // Take screenshot of failed navigation for debugging
        await this.takeErrorScreenshot(
          page,
          application,
          navigationError,
          "navigation_failed"
        );

        throw new Error(
          `Navigation to ${application.propertyUrl} failed: ${navigationError.message}`
        );
      }

      // Wait for CSS to load and page to render properly
      await this.waitForPageToRender(page, application._id);

      loggers.app.debug("Navigation completed successfully", {
        applicationId: application._id,
      });

      // Simulate reading the page before interacting
      const readingDelay = Math.random() * 3000 + 2000; // 2-5 seconds
      loggers.app.debug("Simulating page reading delay", {
        applicationId: application._id,
        delayMs: Math.round(readingDelay),
      });
      await this.sleep(readingDelay);

      // Take screenshot after navigation
      loggers.app.debug("Taking post-navigation screenshot", {
        applicationId: application._id,
      });
      await this.takeScreenshot(page, application, "02_after_navigation");

      // Determine which platform to use based on property URL
      loggers.app.debug("Determining platform handler", {
        applicationId: application._id,
        url: application.propertyUrl,
      });
      const platformHandler = this.getPlatformHandler(application.propertyUrl);

      // Debug: Log the parameters before calling platform handler
      loggers.app.info("DEBUG - Before platform handler call:", {
        handlerName: platformHandler.name,
        hasUserSettings: !!userSettings,
        hasFormData: !!(userSettings && userSettings.formData),
        userSettingsType: typeof userSettings,
        applicationUrl: application.propertyUrl,
      });
      loggers.app.debug("Calling platform-specific handler", {
        applicationId: application._id,
        handlerName: platformHandler.name,
        url: application.propertyUrl,
      });

      const result = await platformHandler.call(
        this,
        page,
        application,
        userSettings
      );

      loggers.app.debug("Platform handler completed successfully", {
        applicationId: application._id,
        handlerName: platformHandler.name,
        result,
      });

      // Take final success screenshot
      loggers.app.debug("Taking final success screenshot", {
        applicationId: application._id,
      });
      await this.takeScreenshot(page, application, "99_success");

      loggers.app.info(
        `Successfully submitted application for property: ${application.propertyUrl}`
      );
      loggers.app.debug("Application submission completed successfully", {
        applicationId: application._id,
        userId: application.userId,
        result,
      });

      return result;
    } catch (error) {
      // Take error screenshot with context
      loggers.app.debug(
        "Error occurred during submission, taking error screenshot",
        {
          applicationId: application._id,
          errorMessage: error.message,
          currentPageUrl: page.url(),
          errorStack: error.stack,
        }
      );
      await this.takeErrorScreenshot(
        page,
        application,
        error,
        "submission_failed"
      );
      loggers.app.error(
        `Failed to submit application for ${application.propertyUrl}:`,
        {
          error: error.message,
          stack: error.stack,
          applicationId: application._id,
          currentUrl: page.url(),
        }
      );
      loggers.app.debug("Application submission failed", {
        applicationId: application._id,
        userId: application.userId,
        errorMessage: error.message,
        errorStack: error.stack,
        currentPageUrl: page.url(),
      });

      // In demo mode, keep browser open longer to observe the error
      if (process.env.DEMO_MODE === "true") {
        loggers.app.info(
          "Demo mode: Keeping browser open for 10 seconds to observe error"
        );
        loggers.app.info(`Error details: ${error.message}`);
        loggers.app.info(`Current page URL: ${page.url()}`);
        await this.sleep(10000);
      }

      throw error;
    } finally {
      loggers.app.debug("Closing browser page", {
        applicationId: application._id,
      });

      // In demo mode, add extra delay before closing
      if (process.env.DEMO_MODE === "true") {
        loggers.app.info(
          "Demo mode: Adding 5 second delay before closing browser"
        );
        await this.sleep(5000);
      }

      await page.close();
    }
  }

  getPlatformHandler(propertyUrl) {
    const url = new URL(propertyUrl);
    const domain = url.hostname.toLowerCase();

    if (domain.includes("funda.nl")) {
      return this.handleFundaApplication;
    } else if (domain.includes("pararius.nl")) {
      return this.handlePariusApplication;
    } else if (domain.includes("kamernet.nl")) {
      return this.handleKamernetApplication;
    } else if (domain.includes("huurwoningen.nl")) {
      return this.handleHuurwoningenApplication;
    } else {
      return this.handleGenericApplication;
    }
  }
  // Funda.nl application handler - Updated for new form structure
  async handleFundaApplication(page, application, userSettings) {
    // Enhanced debug logging with structured data
    loggers.app.info("Starting Funda application handler", {
      applicationId: application._id,
      hasPage: !!page,
      hasApplication: !!application,
      hasUserSettings: !!userSettings,
      formDataAvailable: !!userSettings?.formData,
      userSettingsKeys: userSettings ? Object.keys(userSettings) : [],
    });

    try {
      // Initial page screenshot
      await this.takeScreenshot(page, application, "03_funda_page_loaded");

      // Enhanced cookie handling with better error recovery
      await this.handleCookieConsent(page);

      // Find and click contact button using enhanced strategy
      const contactButton = await this.findContactButton(page, application);
      await this.clickContactButton(page, application, contactButton);

      // Wait and verify form appeared
      await this.waitForContactForm(page);

      // Add human-like behavior before form filling
      await this.addHumanLikeBehavior(page, { applicationId: application._id });

      // Fill the contact form
      await this.fillFundaContactForm(page, userSettings, application);

      // Add human-like behavior after filling
      await this.addHumanLikeBehavior(page, { applicationId: application._id });

      // Take screenshot after filling form
      await this.takeScreenshot(page, application, "06_funda_form_filled");

      // Submit the form
      const submitResult = await this.submitContactForm(page, application);

      // Verify submission success
      const verificationResult = await this.verifySubmission(page);

      // Final success screenshot if verified
      if (verificationResult) {
        await this.takeScreenshot(
          page,
          application,
          "08_funda_success_confirmed"
        );
      }

      return {
        success: true,
        platform: "funda",
        verified: verificationResult,
        clickSuccess: submitResult.clickSuccess,
      };
    } catch (error) {
      loggers.app.error("Funda application handler failed", {
        applicationId: application._id,
        error: error.message,
        stack: error.stack,
      });

      await this.takeErrorScreenshot(
        page,
        application,
        error,
        "funda_handler_failure"
      );

      throw error;
    }
  }

  // Separate method for cookie consent handling
  async handleCookieConsent(page) {
    const cookieStrategies = [
      {
        name: "Enhanced Notice Popup",
        execute: async () => {
          const noticePopup = await page.$('[data-testid="notice"]');
          if (!noticePopup) return false;

          loggers.app.info("Found cookie notice popup");

          const acceptButton = await page.evaluateHandle(() => {
            const notice = document.querySelector('[data-testid="notice"]');
            if (!notice) return null;

            const buttons = notice.querySelectorAll("button");
            for (const button of buttons) {
              const text = button.textContent?.toLowerCase() || "";
              const id = button.id?.toLowerCase() || "";

              if (
                text.includes("alles accepteren") ||
                text.includes("accept all") ||
                id.includes("didomi-notice-agree-button")
              ) {
                return button;
              }
            }
            return null;
          });

          if (acceptButton && acceptButton.asElement()) {
            await acceptButton.asElement().click();
            loggers.app.info("Accepted cookies via notice popup");
            await this.sleep(1500);
            return true;
          }
          return false;
        },
      },
      {
        name: "Standard Cookie Selectors",
        execute: async () => {
          const cookieSelectors = [
            '[data-testid="accept-all-cookies-button"]',
            ".cookie-accept-all",
            "#accept-cookies",
            'button[data-consent="accept-all"]',
            'button:contains("Accept all cookies")',
          ];

          for (const selector of cookieSelectors) {
            try {
              const element = await page.$(selector);
              if (element) {
                await element.click();
                loggers.app.info(`Accepted cookies via selector: ${selector}`);
                await this.sleep(1500);
                return true;
              }
            } catch (e) {
              continue;
            }
          }
          return false;
        },
      },
    ];

    for (const strategy of cookieStrategies) {
      try {
        if (await strategy.execute()) {
          return; // Success, exit
        }
      } catch (e) {
        loggers.app.debug(
          `Cookie strategy ${strategy.name} failed`,
          e.message
        );
      }
    }

    loggers.app.debug("No cookie banner found or already accepted");
  }

  // Enhanced contact button finding with better validation
  async findContactButton(page, application) {
    const contactSelectors = [
      // High priority - most specific
      '[data-optimizely="contact-email"]',
      'a[href*="makelaar-contact"]',
      '[data-testid="contact-block-container"] button',
      '[data-testid="contact-button"]',
      '[data-testid="contact-form-button"]',
      '[data-testid="broker-contact-button"]',

      // Medium priority - data attributes
      'button[data-test-id*="contact"]',
      'button[data-testid*="contact"]',

      // Lower priority - CSS classes
      ".makelaar-contact-button",
      ".broker-contact",
      ".fd-button--contact",
      ".contact-button",

      // Text-based fallbacks
      'a:has-text("Neem contact op")',
      'button:has-text("Contact")',
      'a:has-text("Contact")',
    ];

    // Function to validate button is actually usable
    const validateButton = async (element) => {
      if (!element) return false;

      try {
        const isVisible = await element.isIntersectingViewport();
        const isEnabled = await element.evaluate(
          (el) =>
            !el.disabled &&
            !el.hasAttribute("disabled") &&
            el.offsetParent !== null
        );
        return isVisible && isEnabled;
      } catch (e) {
        return false;
      }
    };

    // Try finding button with progressive scrolling
    const searchWithScrolling = async () => {
      const scrollPositions = [0, 0.25, 0.5, 0.75];

      for (const scrollPos of scrollPositions) {
        // Scroll to position
        await page.evaluate((pos) => {
          window.scrollTo({
            top: document.body.scrollHeight * pos,
            behavior: "smooth",
          });
        }, scrollPos);

        await this.sleep(1000);

        // Try each selector at this scroll position
        for (const selector of contactSelectors) {
          try {
            const element = await page.$(selector);
            if (await validateButton(element)) {
              loggers.app.info(
                `Found contact button: ${selector} at scroll ${ 
                  scrollPos * 100
                }%`
              );
              return element;
            }
          } catch (e) {
            continue;
          }
        }
      }
      return null;
    };

    let contactButton = await searchWithScrolling();

    // Enhanced text/href fallback
    if (!contactButton) {
      try {
        contactButton = await page.evaluateHandle(() => {
          const textPatterns = [
            /neem\s+contact\s+op/i,
            /contact\s+opnemen/i,
            /contacteer/i,
            /contact(?!act)/i, // Avoid "contactact" false matches
          ];

          const hrefPatterns = [
            /makelaar-contact/i,
            /broker-contact/i,
            /contact-form/i,
          ];

          const clickableElements = Array.from(
            document.querySelectorAll('a, button, [role="button"], [onclick]')
          );

          return clickableElements.find((element) => {
            if (element.offsetParent === null || element.disabled) return false;

            const text = (
              element.textContent ||
              element.innerText ||
              ""
            ).trim();
            const href = element.getAttribute("href") || "";
            const ariaLabel = element.getAttribute("aria-label") || "";

            const textMatch = textPatterns.some((pattern) =>
              pattern.test(text)
            );
            const hrefMatch = hrefPatterns.some((pattern) =>
              pattern.test(href)
            );
            const ariaMatch = textPatterns.some((pattern) =>
              pattern.test(ariaLabel)
            );

            return textMatch || hrefMatch || ariaMatch;
          });
        });

        if (contactButton && contactButton.asElement()) {
          contactButton = contactButton.asElement();
          loggers.app.info(
            "Found contact button via enhanced text/href search"
          );
        }
      } catch (e) {
        loggers.app.debug("Text/href search failed:", e.message);
      }
    }

    if (!contactButton) {
      await this.takeErrorScreenshot(
        page,
        application,
        new Error("Contact button not found after exhaustive search"),
        "funda_no_contact_button"
      );
      throw new Error(
        "Contact button not found on Funda page after trying all strategies."
      );
    }

    return contactButton;
  }

  // Enhanced contact button clicking with better error handling
  async clickContactButton(page, application, contactButton) {
    await this.takeScreenshot(
      page,
      application,
      "04_funda_before_contact_click"
    );

    const clickStrategies = [
      {
        name: "Precise Mouse Click with Viewport Validation",
        execute: async () => {
          // Scroll element into center of viewport
          await contactButton.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "center",
          });
          await this.sleep(800);

          const boundingBox = await contactButton.boundingBox();
          if (
            !boundingBox ||
            boundingBox.width <= 0 ||
            boundingBox.height <= 0
          ) {
            throw new Error("Invalid bounding box dimensions");
          }

          // Calculate click coordinates with slight randomization
          const x =
            boundingBox.x + boundingBox.width * (0.3 + Math.random() * 0.4);
          const y =
            boundingBox.y + boundingBox.height * (0.3 + Math.random() * 0.4);

          // Natural mouse movement
          await page.mouse.move(x, y, { steps: 8 });
          await this.sleep(200 + Math.random() * 200);

          // Hover to trigger any hover effects
          await contactButton.hover();
          await this.sleep(150);

          // Click with human-like delay
          await page.mouse.click(x, y, {
            delay: 40 + Math.random() * 60,
          });

          loggers.app.info("Contact button clicked with precise mouse click");
          return true;
        },
      },
      {
        name: "JavaScript Event Simulation",
        execute: async () => {
          await contactButton.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          await this.sleep(500);

          await page.evaluate((element) => {
            // Simulate complete mouse interaction sequence
            const rect = element.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top + rect.height / 2;

            ["mousedown", "mouseup", "click"].forEach((eventType) => {
              const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y,
                button: 0,
                buttons: 1,
              });
              element.dispatchEvent(event);
            });

            // Also trigger focus if element supports it
            if (typeof element.focus === "function") {
              element.focus();
            }
          }, contactButton);

          loggers.app.info("Contact button clicked with JavaScript events");
          return true;
        },
      },
      {
        name: "Direct Element Click",
        execute: async () => {
          await contactButton.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          await this.sleep(400);

          // Try to focus first
          try {
            await contactButton.focus();
            await this.sleep(100);
          } catch (e) {
            // Not all elements are focusable
          }

          await contactButton.click({
            delay: 30 + Math.random() * 40,
          });

          loggers.app.info("Contact button clicked with direct element click");
          return true;
        },
      },
    ];

    let clickSuccess = false;
    let lastError = null;

    for (const strategy of clickStrategies) {
      try {
        loggers.app.info(`Attempting contact click strategy: ${strategy.name}`);

        // Random delay between attempts
        await this.sleep(100 + Math.random() * 200);

        await strategy.execute();

        clickSuccess = true;
        break;
      } catch (error) {
        lastError = error;
        loggers.app.warn(
          `Contact click strategy ${strategy.name} failed`,
          error.message
        );

        // Wait before trying next strategy
        await this.sleep(800 + Math.random() * 400);
      }
    }

    if (!clickSuccess) {
      await this.takeErrorScreenshot(
        page,
        application,
        new Error(`All contact click strategies failed: ${lastError?.message}`),
        "contact_click_failed"
      );

      throw new Error(`Failed to click contact button: ${lastError?.message}`);
    }

    // Wait for form to potentially load
    await this.sleep(2000);

    await this.takeScreenshot(
      page,
      application,
      "05_funda_after_contact_click"
    );
  }

  // Enhanced form waiting with multiple detection strategies
  async waitForContactForm(page) {
    const formDetectionStrategies = [
      // Wait for specific form elements
      async () => {
        const formSelectors = [
          'form[aria-busy="false"]',
          'form:not([aria-busy="true"])',
          '[data-testid="contact-form"]',
          'form[action*="contact"]',
          "form",
        ];

        for (const selector of formSelectors) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            loggers.app.info(`Form detected with selector: ${selector}`);
            return true;
          } catch (e) {
            continue;
          }
        }
        return false;
      },

      // Wait for input fields to appear
      async () => {
        const inputSelectors = [
          'input[type="email"]',
          'input[name*="email"]',
          'textarea[name*="message"]',
          'input[name*="name"]',
        ];

        for (const selector of inputSelectors) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            loggers.app.info(`Form inputs detected with selector: ${selector}`);
            return true;
          } catch (e) {
            continue;
          }
        }
        return false;
      },

      // Wait for URL change (if form is on different page)
      async () => {
        return new Promise((resolve) => {
          const timeout = setTimeout(() => resolve(false), 5000);

          const checkUrl = () => {
            const currentUrl = page.url();
            if (
              currentUrl.includes("contact") ||
              currentUrl.includes("makelaar")
            ) {
              clearTimeout(timeout);
              loggers.app.info("Form detected via URL change");
              resolve(true);
            } else {
              setTimeout(checkUrl, 500);
            }
          };

          checkUrl();
        });
      },
    ];

    // Try each detection strategy
    for (const strategy of formDetectionStrategies) {
      if (await strategy()) {
        return true;
      }
    }

    loggers.app.warn("Form detection strategies failed, proceeding anyway");
    return false;
  }

  // Enhanced submit form handling
  async submitContactForm(page, application) {
    // Debug: Log all buttons for troubleshooting
    const allButtons = await page.evaluate(() => {
      return Array.from(document.querySelectorAll("button")).map((btn) => ({
        type: btn.type || "N/A",
        textContent: (btn.textContent || "").trim().substring(0, 50),
        className: btn.className || "N/A",
        disabled: btn.disabled,
        ariaDisabled: btn.getAttribute("aria-disabled"),
        id: btn.id || "N/A",
      }));
    });
    loggers.app.info("Available buttons on page:", { buttons: allButtons });

    // Enhanced submit button finding
    const findSubmitButton = async () => {
      // Priority-ordered selectors
      const submitSelectors = [
        '#main-content form button[type="submit"]',
        'button[type="submit"]:not([disabled])',
        'button[type="submit"]',
      ];

      // Try CSS selectors first
      for (const selector of submitSelectors) {
        try {
          const button = await page.$(selector);
          if (button) {
            const isEnabled = await button.evaluate(
              (el) =>
                !el.disabled && el.getAttribute("aria-disabled") !== "true"
            );
            if (isEnabled) {
              loggers.app.info(
                `Found submit button with selector: ${selector}`
              );
              return button;
            }
          }
        } catch (e) {
          continue;
        }
      }

      // Text-based search with multiple languages
      const textPatterns = [
        /verstuur\s+bericht/i,
        /send\s+message/i,
        /verstuur/i,
        /verzenden/i,
        /submit/i,
        /send/i,
      ];

      const button = await page.evaluateHandle(() => {
        const buttons = Array.from(document.querySelectorAll("button"));
        const patterns = [
          /verstuur\s+bericht/i,
          /send\s+message/i,
          /verstuur/i,
          /verzenden/i,
          /submit/i,
          /send/i,
        ];

        return buttons.find((button) => {
          if (
            button.disabled ||
            button.getAttribute("aria-disabled") === "true"
          ) {
            return false;
          }

          const buttonText = (
            button.textContent ||
            button.innerText ||
            ""
          ).trim();
          const matches = patterns.some((pattern) => pattern.test(buttonText));

          // Also check nested spans
          if (!matches) {
            const spans = button.querySelectorAll("span");
            return Array.from(spans).some((span) => {
              const spanText = (
                span.textContent ||
                span.innerText ||
                ""
              ).trim();
              return patterns.some((pattern) => pattern.test(spanText));
            });
          }

          return matches;
        });
      });

      if (button && button.asElement()) {
        loggers.app.info("Found submit button via text search");
        return button.asElement();
      }

      return null;
    };

    const submitButton = await findSubmitButton();

    if (!submitButton) {
      loggers.app.warn(
        "No submit button found - form may auto-submit or use different mechanism"
      );
      return { clickSuccess: false, reason: "no_submit_button" };
    }

    // Enhanced click strategies for submit button
    const clickStrategies = [
      {
        name: "Enhanced Mouse Click with Validation",
        execute: async () => {
          await submitButton.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          await this.sleep(500);

          const boundingBox = await submitButton.boundingBox();
          if (
            !boundingBox ||
            boundingBox.width <= 0 ||
            boundingBox.height <= 0
          ) {
            throw new Error("Submit button has invalid dimensions");
          }

          // Calculate randomized click coordinates
          const x =
            boundingBox.x + boundingBox.width * (0.4 + Math.random() * 0.2);
          const y =
            boundingBox.y + boundingBox.height * (0.4 + Math.random() * 0.2);

          // Natural mouse movement
          await page.mouse.move(x, y, { steps: 6 });
          await this.sleep(150);

          // Hover to trigger any CSS states
          await submitButton.hover();
          await this.sleep(100);

          // Click with realistic delay
          await page.mouse.click(x, y, {
            delay: 60 + Math.random() * 40,
          });

          return true;
        },
      },
      {
        name: "JavaScript Click with Event Simulation",
        execute: async () => {
          await page.evaluate((button) => {
            // Scroll into view
            button.scrollIntoView({ behavior: "smooth", block: "center" });

            // Simulate mouse events
            const events = ["mousedown", "mouseup", "click"];
            events.forEach((eventType) => {
              const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true,
                button: 0,
                buttons: 1,
              });
              button.dispatchEvent(event);
            });

            // Also trigger click directly
            button.click();
          }, submitButton);

          return true;
        },
      },
      {
        name: "Direct Element Click",
        execute: async () => {
          await submitButton.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          await this.sleep(300);

          await submitButton.click({
            delay: 40 + Math.random() * 30,
          });

          return true;
        },
      },
    ];

    let clickSuccess = false;
    let lastError = null;

    for (const strategy of clickStrategies) {
      try {
        loggers.app.info(`Attempting submit click strategy: ${strategy.name}`);

        // Pre-click delay
        await this.sleep(200 + Math.random() * 300);

        await strategy.execute();

        loggers.app.info(`✅ Submit ${strategy.name} successful`);
        clickSuccess = true;
        break;
      } catch (error) {
        lastError = error;
        loggers.app.warn(`❌ Submit ${strategy.name} failed:`, error.message);

        // Wait before next attempt
        await this.sleep(1000);
      }
    }

    if (clickSuccess) {
      // Post-click wait for submission to process
      await this.sleep(3000);
      await this.takeScreenshot(page, application, "07_funda_after_submit");
    } else {
      loggers.app.error("All submit click strategies failed", {
        lastError: lastError?.message,
        applicationId: application._id,
      });

      await this.takeErrorScreenshot(
        page,
        application,
        new Error(`Submit click failed: ${lastError?.message}`),
        "submit_click_failed"
      );

      // Don't throw - continue to verification
      loggers.app.warn("Continuing despite submit click failure");
    }

    return {
      clickSuccess,
      error: lastError?.message,
      attempted: true,
    };
  }

  // Pararius.nl application handler
  async handlePariusApplication(page, application, userSettings) {
    // Take screenshot of Pararius page
    await this.takeScreenshot(page, application, "03_pararius_page_loaded");

    // Look for contact/apply button
    const contactButton = await page.$
      ('.contact-button, button:contains("Contact"), .btn-contact'
      );

    if (!contactButton) {
      await this.takeErrorScreenshot(
        page,
        application,
        new Error("Contact button not found"),
        "pararius_no_contact_button"
      );
      throw new Error("Contact button not found on Pararius page");
    }

    await this.takeScreenshot(
      page,
      application,
      "04_pararius_before_contact_click"
    );
    await contactButton.click();
    await this.sleep(2000);

    await this.takeScreenshot(
      page,
      application,
      "05_pararius_after_contact_click"
    );

    await this.fillContactForm(
      page,
      userSettings,
      {
        nameSelector: 'input[name="name"], #contact_name',
        emailSelector: 'input[name="email"], #contact_email',
        phoneSelector: 'input[name="phone"], #contact_phone',
        messageSelector: 'textarea[name="message"], #contact_message',
      },
      application
    );

    await this.takeScreenshot(page, application, "06_pararius_form_filled");

    const submitButton = await page.$('button[type="submit"], .btn-submit');
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "pararius" };
  }

  // Kamernet.nl application handler
  async handleKamernetApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    // Kamernet often requires login first
    const loginRequired = await page.$(".login-required, .member-only");
    if (loginRequired) {
      throw new Error("Kamernet requires login - manual intervention needed");
    }

    const reactButton = await page.$
      ('.react-button, button:contains("Reageren")'
      );
    if (!reactButton) {
      throw new Error("React button not found on Kamernet page");
    }

    await reactButton.click();
    await this.sleep(2000);

    await this.fillContactForm(page, userSettings, {
      nameSelector: 'input[name="name"], #name',
      emailSelector: 'input[name="email"], #email',
      phoneSelector: 'input[name="phone"], #phone',
      messageSelector: 'textarea[name="message"], #message',
    });

    const submitButton = await page.$('button[type="submit"], .submit-button');
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "kamernet" };
  }

  // Huurwoningen.nl application handler
  async handleHuurwoningenApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    const applyButton = await page.$
      ('.apply-button, button:contains("Solliciteren"), .contact-button'
      );

    if (!applyButton) {
      throw new Error("Apply button not found on Huurwoningen page");
    }

    await applyButton.click();
    await this.sleep(2000);

    await this.fillContactForm(page, userSettings, {
      nameSelector: 'input[name="name"], #applicant_name',
      emailSelector: 'input[name="email"], #applicant_email',
      phoneSelector: 'input[name="phone"], #applicant_phone',
      messageSelector: 'textarea[name="message"], #applicant_message',
    });

    // Handle additional fields common on Huurwoningen
    await this.fillAdditionalFields(page, userSettings);

    const submitButton = await page.$
      ('button[type="submit"], .submit-application'
      );
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "huurwoningen" };
  }
  // Generic handler for unknown platforms
  async handleGenericApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    // Try to find common contact/apply buttons
    const buttonSelectors = [
      'button:contains("Contact")',
      'button:contains("Apply")',
      'button:contains("Reageren")',
      'button:contains("Solliciteren")',
      ".contact-button",
      ".apply-button",
      ".btn-contact",
      ".btn-apply",
    ];

    let foundButton = false;
    for (const selector of buttonSelectors) {
      const button = await page.$(selector);
      if (button) {
        await button.click();
        await this.sleep(2000);
        foundButton = true;
        break;
      }
    }

    if (!foundButton) {
      throw new Error("No contact/apply button found on page");
    }

    // Try generic form filling
    await this.fillContactForm(page, userSettings, {
      nameSelector:
        'input[name="name"], input[name="full_name"], input[name="fullname"], #name, #full_name',
      emailSelector: 'input[name="email"], input[type="email"], #email',
      phoneSelector:
        'input[name="phone"], input[name="telephone"], input[type="tel"], #phone, #telephone',
      messageSelector:
        'textarea[name="message"], textarea[name="comment"], textarea[name="remarks"], #message, #comment',
    });

    // Try to submit
    const submitSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send")',
      'button:contains("Verstuur")',
      'button:contains("Submit")',
      ".submit-button",
      ".btn-submit",
    ];

    for (const selector of submitSelectors) {
      const submitButton = await page.$(selector);
      if (submitButton) {
        await submitButton.click();
        await this.sleep(3000);
        break;
      }
    }

    return { success: true, platform: "generic" };
  }

  // Helper method to fill Funda's new contact form structure
  async fillFundaContactForm(page, userSettings, application) {
    const applicationId = application._id;

    // Debug logging to identify the issue
    loggers.app.info("DEBUG fillFundaContactForm called with:", {
      hasUserSettings: !!userSettings,
      userSettingsType: typeof userSettings,
      hasFormData: !!(userSettings && userSettings.formData),
      userSettingsKeys: userSettings ? Object.keys(userSettings) : "N/A",
      applicationId,
    });

    if (!userSettings) {
      const error = new Error(
        "userSettings is undefined in fillFundaContactForm"
      );
      loggers.app.debug("Form filling failed - userSettings undefined", {
        applicationId,
      });
      throw error;
    }

    if (!userSettings.formData) {
      loggers.app.error(
        "userSettings.formData is undefined. Full userSettings:",
        userSettings
      );
      const error = new Error(
        "userSettings.formData is undefined in fillFundaContactForm"
      );
      loggers.app.debug("Form filling failed - formData undefined", {
        applicationId,
        userSettings,
      });
      throw error;
    }

    const formData = userSettings.formData;

    loggers.app.info("Starting Funda form filling process", {
      formData: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
      },
      applicationId,
    });

    try {
      // Debug: Log all form elements on the page
      loggers.app.debug("Analyzing page form elements", { applicationId });
      const allFormElements = await page.evaluate(() => {
        const inputs = Array.from(
          document.querySelectorAll("input, textarea, select")
        );
        return inputs.map((el) => ({
          tagName: el.tagName,
          type: el.type || "N/A",
          id: el.id || "N/A",
          name: el.name || "N/A",
          className: el.className || "N/A",
          placeholder: el.placeholder || "N/A",
        }));
      });
      loggers.app.info("All form elements found on page:", {
        elements: allFormElements,
        elementCount: allFormElements.length,
        applicationId,
      });

      // Fill the question/message textarea
      loggers.app.info("Looking for question textarea...");
      loggers.app.debug("Searching for question textarea field", {
        applicationId,
      });
      const questionTextarea = await page.$("#questionInput");
      if (questionTextarea) {
        loggers.app.info("Found question textarea, clearing and filling...");
        loggers.app.debug("Question textarea found, proceeding with filling", {
          applicationId,
        });

        const message = this.generateApplicationMessage(userSettings);
        loggers.app.info("Generated message, setting value directly...", {
          messageLength: message.length,
          applicationId,
        });

        // Use human-like typing instead of direct value setting for better stealth
        await questionTextarea.focus();
        loggers.app.debug("Question textarea focused", { applicationId });
        await this.sleep(100);

        // Clear field first
        loggers.app.debug("Clearing existing content from question textarea", {
          applicationId,
        });
        await page.keyboard.down("Control");
        await page.keyboard.press("KeyA");
        await page.keyboard.up("Control");
        await page.keyboard.press("Backspace");
        await this.sleep(50);

        // Use anti-detection system for human-like typing if available
        try {
          loggers.app.debug("Attempting anti-detection typing", {
            applicationId,
            messageLength: message.length,
          });
          await this.antiDetection.simulateTyping(
            page,
            "#questionInput",
            message,
            {
              minDelay: 10,
              maxDelay: 40,
              mistakes: true,
            }
          );
          loggers.app.info("Used anti-detection typing");
          loggers.app.debug("Anti-detection typing completed successfully", {
            applicationId,
          });
        } catch (antiDetectionError) {
          loggers.app.warn("Anti-detection typing failed, using fallback", {
            error: antiDetectionError.message,
            applicationId,
          });
          // Fallback to manual typing with random delays
          const typingDelay = Math.random() * 20 + 10; // Random delay between 10-30ms per character
          loggers.app.debug("Using fallback typing with delay", {
            applicationId,
            delayPerChar: Math.round(typingDelay),
          });
          await page.keyboard.type(message, {
            delay: typingDelay,
          });
        }
        loggers.app.info("Filled question textarea successfully");
        loggers.app.debug("Question textarea filling completed", {
          applicationId,
        });
      } else {
        loggers.app.warn("Question textarea not found");
        loggers.app.debug("Question textarea not found on page", {
          applicationId,
        });
      }

      // Check the viewing request checkbox if needed
      loggers.app.debug("Looking for viewing request checkbox", {
        applicationId,
      });
      const viewingCheckbox = await page.$("#checkbox-viewingRequest");
      if (viewingCheckbox) {
        const isChecked = await page.evaluate(
          (el) => el.checked,
          viewingCheckbox
        );
        loggers.app.debug("Viewing checkbox found", {
          applicationId,
          currentlyChecked: isChecked,
        });

        if (!isChecked) {
          await viewingCheckbox.click();
          loggers.app.info("Checked viewing request checkbox");
          loggers.app.debug("Viewing request checkbox checked", {
            applicationId,
          });
        } else {
          loggers.app.debug("Viewing request checkbox already checked", {
            applicationId,
          });
        }
      } else {
        loggers.app.debug("Viewing request checkbox not found", {
          applicationId,
        });
      }

      // Fill email address (should be pre-filled but let's ensure it's correct)
      loggers.app.debug("Processing email field", {
        applicationId,
        email: formData.email,
      });
      const emailField = await page.$("#emailAddress");
      if (emailField) {
        await page.evaluate(
          (el, email) => {
            if (el) {
              el.value = email;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          emailField,
          formData.email
        );
        loggers.app.info("Filled email field successfully");
        loggers.app.debug("Email field filled successfully", { applicationId });
      } else {
        // Fallback selectors
        loggers.app.debug(
          "Primary email field not found, trying fallback selectors",
          { applicationId }
        );
        const emailSelectors = [
          'input[type="email"]',
          'input[name="email"]',
          'input[data-testid*="email"]',
          'input[placeholder*="email" i]',
        ];
        let filled = false;
        for (const sel of emailSelectors) {
          try {
            const el = await page.$(sel);
            if (el) {
              await page.evaluate(
                (node, email) => {
                  if (node) {
                    node.value = email;
                    node.dispatchEvent(new Event("input", { bubbles: true }));
                    node.dispatchEvent(new Event("change", { bubbles: true }));
                  }
                },
                el,
                formData.email
              );
              loggers.app.info(`Filled email using fallback selector: ${sel}`);
              loggers.app.debug("Email field filled with fallback selector", {
                applicationId,
                selector: sel,
              });
              filled = true;
              break;
            }
          } catch (selectorError) {
            loggers.app.debug("Fallback email selector failed", {
              applicationId,
              selector: sel,
              error: selectorError.message,
            });
          }
        }
        if (!filled) {
          loggers.app.warn("Email field not found");
          loggers.app.debug("Email field not found with any selector", {
            applicationId,
          });
        }
      }

      // Fill first name
      loggers.app.debug("Processing first name field", {
        applicationId,
        firstName: formData.firstName,
      });
      const firstNameField = await page.$("#firstName");
      if (firstNameField) {
        await page.evaluate(
          (el, name) => {
            if (el) {
              el.value = name;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          firstNameField,
          formData.firstName
        );
        loggers.app.info("Filled first name field successfully");
        loggers.app.debug("First name field filled successfully", {
          applicationId,
        });
      } else {
        loggers.app.warn("First name field not found");
        loggers.app.debug("First name field not found", { applicationId });
      }

      // Fill last name
      loggers.app.debug("Processing last name field", {
        applicationId,
        lastName: formData.lastName,
      });
      const lastNameField = await page.$("#lastName");
      if (lastNameField) {
        await page.evaluate(
          (el, name) => {
            if (el) {
              el.value = name;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          lastNameField,
          formData.lastName
        );
        loggers.app.info("Filled last name field successfully");
        loggers.app.debug("Last name field filled successfully", {
          applicationId,
        });
      } else {
        loggers.app.warn("Last name field not found");
        loggers.app.debug("Last name field not found", { applicationId });
      }

      // Fill phone number with multiple format attempts
      loggers.app.debug("Processing phone field", {
        applicationId,
        phone: formData.phone,
      });
      const phoneField = await page.$("#phoneNumber");
      if (phoneField) {
        // Try different phone number formats (prioritize format that works: 0612345678)
        const phoneFormats = [
          formData.phone.replace(/[\s\-\+]/g, ""), // Remove all spaces, dashes, plus (WORKS!)
          formData.phone, // Original format
          formData.phone.replace(/[\-\+]/g, "").replace(/\s+/g, " "), // Keep single spaces only
          `0${formData.phone
            .replace(/[\s\-\+]/g, "")
            .replace(/^(\+31|31|0)/, "")}`, // Ensure starts with 0
        ];

        loggers.app.debug("Attempting phone number formats", {
          applicationId,
          originalPhone: formData.phone,
          formatCount: phoneFormats.length,
          formats: phoneFormats,
        });

        let phoneSet = false;
        for (const phoneFormat of phoneFormats) {
          if (phoneSet) break;

          await page.evaluate(
            (el, phone) => {
              if (el) {
                el.value = phone;
                el.dispatchEvent(new Event("input", { bubbles: true }));
                el.dispatchEvent(new Event("change", { bubbles: true }));
                el.dispatchEvent(new Event("blur", { bubbles: true }));
              }
            },
            phoneField,
            phoneFormat
          );

          loggers.app.info(`Trying phone format: ${phoneFormat}`);
          loggers.app.debug("Phone format applied, waiting for validation", {
            applicationId,
            format: phoneFormat,
          });

          // Wait a moment for validation to complete
          await this.sleep(800);

          // Check if there's a validation error with more comprehensive selectors
          const validationSelectors = [
            ".error",
            ".validation-error",
            '[class*="error"]',
            '[class*="invalid"]',
            ".field-error",
            ".input-error",
            '[role="alert"]',
            ".text-red-500",
            ".text-danger",
          ];

          let validationError = null;
          let errorText = "";

          for (const selector of validationSelectors) {
            validationError = await page.$(selector);
            if (validationError) {
              errorText = await page.evaluate(
                (el) => el.textContent,
                validationError
              );
              if (errorText && errorText.trim()) {
                loggers.app.warn("Validation error detected:", {
                  selector: selector,
                  error: errorText.trim(),
                  phoneFormat: phoneFormat,
                  applicationId,
                });
                break;
              }
            }
          }

          // If no validation error, we're good
          if (!errorText || !errorText.trim()) {
            loggers.app.info(`Phone format accepted: ${phoneFormat}`);
            loggers.app.debug("Phone format validation passed", {
              applicationId,
              acceptedFormat: phoneFormat,
            });
            phoneSet = true;
          } else {
            loggers.app.debug("Phone format validation failed", {
              applicationId,
              format: phoneFormat,
              error: errorText.trim(),
            });
          }
        }

        if (!phoneSet) {
          loggers.app.warn("All phone formats failed validation");
          loggers.app.debug("All phone format attempts failed", {
            applicationId,
            attemptedFormats: phoneFormats,
          });
        }
      } else {
        loggers.app.warn("Phone field not found");
        loggers.app.debug("Phone field not found", { applicationId });
      }

      const sleepDelay = 200; // Reduced sleep time for faster execution
      loggers.app.debug("Form filling completed, adding final delay", {
        applicationId,
        delayMs: sleepDelay,
      });
      await this.sleep(sleepDelay);

      loggers.app.info("Funda form filling completed");
      loggers.app.debug("Funda form filling process completed successfully", {
        applicationId,
        filledFields: {
          questionTextarea: !!(await page.$("#questionInput")) ,
          emailField: !!(await page.$("#emailAddress")) ,
          firstNameField: !!(await page.$("#firstName")) ,
          lastNameField: !!(await page.$("#lastName")) ,
          phoneField: !!(await page.$("#phoneNumber")) ,
        },
      });
    } catch (error) {
      loggers.app.error("Error filling Funda form:", error);
      loggers.app.debug("Funda form filling failed", {
        applicationId,
        errorMessage: error.message,
        errorStack: error.stack,
      });
      throw error;
    }
  }

  // Helper method to fill contact forms (legacy method for other platforms)
  async fillContactForm(page, userSettings, selectors, application) {
    const formData = userSettings.formData;

    loggers.app.info("Starting form filling process", {
      selectors: selectors,
      formData: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
      },
    });

    // Fill name
    const nameField = await page.$(selectors.nameSelector);
    if (nameField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, nameField);
      await nameField.type(`${formData.firstName} ${formData.lastName}`);
      loggers.app.info("Filled name field successfully");
    } else {
      loggers.app.warn("Name field not found", {
        selector: selectors.nameSelector,
      });
    }

    // Fill email
    const emailField = await page.$(selectors.emailSelector);
    if (emailField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, emailField);
      await emailField.type(formData.email);
      loggers.app.info("Filled email field successfully");
    } else {
      loggers.app.warn("Email field not found", {
        selector: selectors.emailSelector,
      });
    }

    // Fill phone
    const phoneField = await page.$(selectors.phoneSelector);
    if (phoneField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, phoneField);
      await phoneField.type(formData.phone);
      loggers.app.info("Filled phone field successfully");
    } else {
      loggers.app.warn("Phone field not found", {
        selector: selectors.phoneSelector,
      });
    }

    // Fill message
    const messageField = await page.$(selectors.messageSelector);
    if (messageField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, messageField);
      const message = this.generateApplicationMessage(userSettings);
      await messageField.type(message);
      loggers.app.info("Filled message field successfully", {
        messageLength: message.length,
      });
    } else {
      loggers.app.warn("Message field not found", {
        selector: selectors.messageSelector,
      });
    }

    await this.sleep(1000);
    loggers.app.info("Form filling completed");
  }

  // Helper method for additional fields
  async fillAdditionalFields(page, userSettings) {
    const formData = userSettings.formData;

    // Income field
    const incomeField = await page.$
      ('input[name="income"], input[name="salary"], #income, #salary'
      );
    if (incomeField && formData.monthlyIncome) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, incomeField);
      await incomeField.type(formData.monthlyIncome.toString());
    }

    // Age/Birth date
    const ageField = await page.$
      ('input[name="age"], input[name="birth_date"], #age'
      );
    if (ageField && formData.age) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, ageField);
      await ageField.type(formData.age.toString());
    }

    // Occupation
    const occupationField = await page.$
      ('input[name="occupation"], input[name="job"], #occupation'
      );
    if (occupationField && formData.occupation) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, occupationField);
      await occupationField.type(formData.occupation);
    }

    // Move-in date
    const moveInField = await page.$
      ('input[name="move_in_date"], input[name="available_from"], #move_in_date'
      );
    if (moveInField && formData.preferredMoveInDate) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, moveInField);
      await moveInField.type(formData.preferredMoveInDate);
    }
  }

  // Test form automation without actually submitting
  async testFormAutomation(listingUrl, options = {}) {
    const { dryRun = true, userId } = options;

    loggers.app.info("Starting form automation test", {
      listingUrl,
      dryRun,
      userId,
    });

    // Launch browser and create page with stealth
    // Note: testFormAutomation uses its own launcher; we still apply stealth at page level

    let browser = null;
    let page = null;
    const testResult = {
      success: false,
      platform: null,
      formType: null,
      fieldsFound: 0,
      formFields: [],
      screenshots: [],
      logs: [],
      errors: [],
      successProbability: 0,
    };

    try {
      // Launch browser
      browser = await this.launchBrowser();
      page = await browser.newPage();

      // Apply stealth to test page
      try {
        await setupPageStealth(page);
        testResult.logs.push("Applied stealth settings to test page");
      } catch (e) {
        testResult.logs.push(`Failed to apply stealth settings: ${e?.message}`);
      }

      // Extra HTTP headers
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
      });

      // Set user agent and viewport
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      );
      await page.setViewport({ width: 1366, height: 768 });

      testResult.logs.push("Browser launched successfully");

      // Navigate to the listing
      await page.goto(listingUrl, {
        waitUntil: "networkidle2",
        timeout: 30000,
      });
      testResult.logs.push("Page loaded successfully");

      // Determine platform
      const url = new URL(listingUrl);
      const domain = url.hostname.toLowerCase();

      if (domain.includes("funda.nl")) {
        testResult.platform = "funda";
        await this.testFundaFormAutomation(page, testResult);
      } else {
        testResult.platform = "generic";
        testResult.logs.push(
          "Generic platform detected - limited testing available"
        );
      }

      testResult.success = true;
      testResult.logs.push("Form automation test completed successfully");
    } catch (error) {
      testResult.errors.push(error.message);
      testResult.logs.push(`Test failed: ${error.message}`);
      loggers.app.error("Form automation test failed:", error);
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (error) {
          // Ignore close errors
        }
      }
      if (browser) {
        try {
          await browser.close();
        } catch (error) {
          // Ignore close errors
        }
      }
    }

    return testResult;
  }

  // Test Funda form automation specifically
  async testFundaFormAutomation(page, testResult) {
    try {
      // Take initial screenshot
      const screenshot1 = await page.screenshot({ encoding: "base64" });
      testResult.screenshots.push({
        name: "initial_page",
        data: screenshot1,
      });

      // Test contact button selectors
      const contactSelectors = [
        '[data-optimizely="contact-email"]',
        'a:contains("Contact")',
        'button:contains("Contact")',
        '[data-testid="contact-block-container"] button',
        ".contact-button",
        'button[data-test-id*="contact"]',
      ];

      let contactButtonFound = false;
      let workingSelector = null;

      for (const selector of contactSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            contactButtonFound = true;
            workingSelector = selector;
            testResult.logs.push(
              `Contact button found with selector: ${selector}`
            );
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      if (!contactButtonFound) {
        // Try scrolling and searching again
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight / 2);
        });
        await this.sleep(2000);

        for (const selector of contactSelectors) {
          try {
            const element = await page.$(selector);
            if (element) {
              contactButtonFound = true;
              workingSelector = selector;
              testResult.logs.push(
                `Contact button found after scrolling with selector: ${selector}`
              );
              break;
            }
          } catch (error) {
            // Continue to next selector
          }
        }
      }

      if (contactButtonFound) {
        testResult.formFields.push({
          name: "contactButton",
          type: "button",
          selector: workingSelector,
          required: true,
          found: true,
        });
        testResult.fieldsFound++;
        testResult.successProbability += 30;
      } else {
        testResult.errors.push("Contact button not found with any selector");
      }

      // Test form field selectors (these would appear after clicking contact button)
      const formFieldSelectors = {
        questionField: "#questionInput",
        viewingCheckbox: "#checkbox-viewingRequest",
        emailField: "#emailAddress",
        firstNameField: "#firstName",
        lastNameField: "#lastName",
        phoneField: "#phoneNumber",
        submitButton: 'button[type="submit"]',
      };

      Object.entries(formFieldSelectors).forEach(([fieldName, selector]) => {
        testResult.formFields.push({
          name: fieldName,
          type: fieldName.includes("button")
            ? "button"
            : fieldName.includes("checkbox")
            ? "checkbox"
            : "input",
          selector: selector,
          required: fieldName !== "viewingCheckbox",
          found: false, // Would be tested after clicking contact button
        });
      });

      testResult.fieldsFound += Object.keys(formFieldSelectors).length;
      testResult.successProbability += contactButtonFound ? 60 : 20;
      testResult.formType = "funda_contact_form";

      testResult.logs.push(
        `Form automation test completed for Funda. Success probability: ${testResult.successProbability}%`
      );
    } catch (error) {
      testResult.errors.push(`Funda test error: ${error.message}`);
      throw error;
    }
  }

  // Generate personalized application message
  generateApplicationMessage(userSettings) {
    const formData = userSettings.formData;
    const templates = userSettings.messageTemplates || {};

    if (templates.default) {
      return this.replacePlaceholders(templates.default, formData);
    }

    // Default template
    return `Beste verhuurder,

Ik ben zeer geïnteresseerd in deze woning. Ik ben ${ 
      formData.age
    } jaar oud, werk als ${formData.occupation} en heb een maandinkomen van €${ 
      formData.monthlyIncome
    }.

${ 
      formData.additionalInfo ||
      "Ik ben een betrouwbare huurder en zorg goed voor de woning."
}

Ik hoor graag van u.

Met vriendelijke groet,
${formData.firstName} ${formData.lastName}`;
  }

  // Replace placeholders in message templates
  replacePlaceholders(template, formData) {
    return template
      .replace(/\{firstName\}/g, formData.firstName)
      .replace(/\{lastName\}/g, formData.lastName)
      .replace(/\{age\}/g, formData.age)
      .replace(/\{occupation\}/g, formData.occupation)
      .replace(/\{monthlyIncome\}/g, formData.monthlyIncome)
      .replace(/\{email\}/g, formData.email)
      .replace(/\{phone\}/g, formData.phone)
      .replace(/\{additionalInfo\}/g, formData.additionalInfo || "");
  }

  // CAPTCHA detection method removed as per user request - automation will proceed automatically
  async handleCaptcha(page) {
    // CAPTCHA detection disabled - always return no CAPTCHA detected
    loggers.app.debug("CAPTCHA detection disabled, proceeding with automation");
    return;
  }

  // Check if application was successful
  async verifySubmission(page) {
    try {
      // Check for specific Funda success message
      const fundaSuccessMessage = await page.evaluate(() => {
        const pageText =
          document.body.textContent || document.body.innerText || "";
        return (
          pageText.includes("Gelukt, je aanvraag is binnen") ||
          pageText.includes("Je hoort snel van de makelaar")
        );
      });

      if (fundaSuccessMessage) {
        loggers.app.info(
          'Funda success message detected: "Gelukt, je aanvraag is binnen"'
        );
        return true;
      }

      // Check for success indicators in HTML structure
      const successSelectors = [
        'h2:contains("Gelukt, je aanvraag is binnen")',
        ".success-message",
        ".confirmation",
        '[class*="success"]',
      ];

      for (const selector of successSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            loggers.app.info(
              `Success indicator found with selector: ${selector}`
            );
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      // Fallback: check for generic success terms
      const genericSuccess = await page.evaluate(() => {
        const pageText =
          document.body.textContent || document.body.innerText || "";
        return (
          pageText.includes("bedankt") ||
          pageText.includes("thank you") ||
          pageText.includes("verzonden") ||
          pageText.includes("sent") ||
          pageText.includes("makelaar mailt je")
        );
      });

      if (genericSuccess) {
        loggers.app.info("Generic success message detected");
        return true;
      }

      loggers.app.debug("No success indicators found on page");
      return false;
    } catch (error) {
      loggers.app.warn("Error verifying submission success:", error.message);
      return false;
    }
  }
}

module.exports = FormAutomationService;