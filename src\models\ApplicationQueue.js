const mongoose = require("mongoose");

const applicationQueueSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },

    listingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Listing",
      required: true,
    },

    listingUrl: {
      type: String,
      required: true,
    },

    // Queue management
    priority: {
      type: Number,
      default: 0,
    },

    status: {
      type: String,
      enum: [
        "pending",
        "processing",
        "completed",
        "failed",
        "retrying",
        "cancelled",
        "paused",
      ],
      default: "pending",
    },

    // Retry mechanism
    attempts: {
      type: Number,
      default: 0,
    },

    maxAttempts: {
      type: Number,
      default: 3,
    },

    // Scheduling
    scheduledAt: {
      type: Date,
      required: true,
    },

    processedAt: { type: Date },

    completedAt: { type: Date },

    // Rate limiting and timing
    delayUntil: {
      type: Date,
    },

    randomDelay: {
      type: Number,
      default: 0,
    }, // in milliseconds

    // Application data
    applicationData: {
      personalInfo: {
        fullName: { type: String, required: true },
        email: { type: String, required: true },
        phone: { type: String, required: true },
        dateOfBirth: { type: Date },
        nationality: { type: String },
        occupation: { type: String },
        employer: { type: String },
        monthlyIncome: { type: Number },
        moveInDate: { type: Date },
        leaseDuration: { type: Number },
        numberOfOccupants: { type: Number },
        hasGuarantor: { type: Boolean },
        guarantorInfo: {
          name: { type: String },
          email: { type: String },
          phone: { type: String },
          relationship: { type: String },
        },
        emergencyContact: {
          name: { type: String },
          phone: { type: String },
          email: { type: String },
          relationship: { type: String },
        },
      },

      preferences: {
        template: {
          type: String,
          enum: ["professional", "casual", "student", "expat"],
        },
        language: { type: String, enum: ["dutch", "english"] },
        customMessage: { type: String },
      },

      documents: [
        {
          type: { type: String },
          filename: { type: String },
          documentId: { type: mongoose.Schema.Types.ObjectId, ref: "Document" },
        },
      ],
    },

    // Generated content
    generatedContent: {
      subject: { type: String },
      message: { type: String },
      personalizedElements: [{ type: String }],
      aiModel: { type: String },
      generatedAt: { type: Date },
      confidence: { type: Number }, // 0-100
    },

    // Error tracking
    errors: [
      {
        message: { type: String },
        code: { type: String },
        timestamp: { type: Date, default: Date.now },
        attempt: { type: Number },
        stack: { type: String },
        category: {
          type: String,
          enum: ["network", "form", "detection", "data", "system"],
        },
      },
    ],

    // Processing metadata
    metadata: {
      formType: { type: String },
      detectionMethods: [{ type: String }],
      processingTime: { type: Number }, // in milliseconds
      browserSession: { type: String },
      userAgent: { type: String },
      ipAddress: { type: String },
      screenshots: [{ type: String }],
      formData: { type: mongoose.Schema.Types.Mixed },
      submissionResponse: { type: mongoose.Schema.Types.Mixed },
    },

    // Automation result from FormAutomationService
    automationResult: {
      success: { type: Boolean },
      platform: { type: String },
      verified: { type: Boolean },
      message: { type: String },
      confirmationNumber: { type: String },
      submissionId: { type: String },
      timestamp: { type: Date },
      processingTime: { type: Number }, // in milliseconds
      screenshots: [{ type: String }],
      errors: [{ type: String }],
    },

    // Status reason for failed applications
    statusReason: { type: String },

    // Anti-detection measures
    antiDetection: {
      fingerprintUsed: { type: String },
      delaysApplied: [
        {
          type: { type: String },
          duration: { type: Number },
        },
      ],
      humanBehaviorSimulated: { type: Boolean, default: false },
      captchaEncountered: { type: Boolean, default: false },
      blockingDetected: { type: Boolean, default: false },
    },

    // Listing snapshot at time of queuing
    listingSnapshot: {
      title: { type: String },
      address: { type: String },
      price: { type: Number },
      rooms: { type: Number },
      size: { type: Number },
      propertyType: { type: String },
      description: { type: String },
      landlord: { type: String },
      availableFrom: { type: Date },
      scrapedAt: { type: Date },
    },

    // Timestamps
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
  },
  {
    suppressReservedKeysWarning: true,
  }
);

// Compound indexes for performance optimization
applicationQueueSchema.index({ status: 1, scheduledAt: 1 });
applicationQueueSchema.index({ userId: 1, status: 1 });
applicationQueueSchema.index({ listingId: 1, userId: 1 }, { unique: true });
applicationQueueSchema.index({ priority: -1, scheduledAt: 1 });
applicationQueueSchema.index({ delayUntil: 1 }, { sparse: true });
applicationQueueSchema.index({ createdAt: -1 });
applicationQueueSchema.index(
  { "metadata.browserSession": 1 },
  { sparse: true }
);

// Virtual fields
applicationQueueSchema.virtual("isReady").get(function () {
  const now = new Date();
  return (
    this.status === "pending" &&
    this.scheduledAt <= now &&
    (!this.delayUntil || this.delayUntil <= now)
  );
});

applicationQueueSchema.virtual("canRetry").get(function () {
  return this.status === "failed" && this.attempts < this.maxAttempts;
});

applicationQueueSchema.virtual("isExpired").get(function () {
  // Consider items expired after 24 hours
  const expiryTime = 24 * 60 * 60 * 1000;
  return Date.now() - this.createdAt.getTime() > expiryTime;
});

applicationQueueSchema.virtual("waitTime").get(function () {
  if (this.processedAt && this.createdAt) {
    return this.processedAt.getTime() - this.createdAt.getTime();
  }
  return null;
});

applicationQueueSchema.virtual("processingTime").get(function () {
  if (this.completedAt && this.processedAt) {
    return this.completedAt.getTime() - this.processedAt.getTime();
  }
  return this.metadata.processingTime || null;
});

// Pre-save middleware
applicationQueueSchema.pre("save", function (next) {
  this.updatedAt = new Date();

  // Set processed time when status changes to processing
  if (
    this.isModified("status") &&
    this.status === "processing" &&
    !this.processedAt
  ) {
    this.processedAt = new Date();
  }

  // Set completed time when status changes to completed or failed
  if (
    this.isModified("status") &&
    ["completed", "failed", "cancelled"].includes(this.status) &&
    !this.completedAt
  ) {
    this.completedAt = new Date();
  }

  // Calculate random delay if not set
  if (this.isNew && !this.randomDelay) {
    // Random delay between 2-10 minutes (120000-600000 ms)
    this.randomDelay =
      Math.floor(Math.random() * (600000 - 120000 + 1)) + 120000;
  }

  next();
});

// Static methods
applicationQueueSchema.statics.getNextPendingItem = function () {
  const now = new Date();
  return this.findOne({
    status: "pending",
    scheduledAt: { $lte: now },
    $or: [{ delayUntil: { $exists: false } }, { delayUntil: { $lte: now } }],
  }).sort({ priority: -1, scheduledAt: 1 });
};

applicationQueueSchema.statics.getQueueStats = function () {
  return this.aggregate([
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
        avgProcessingTime: { $avg: "$metadata.processingTime" },
      },
    },
  ]);
};

applicationQueueSchema.statics.findByUser = function (userId, status = null) {
  const query = { userId };
  if (status) query.status = status;
  return this.find(query).sort({ createdAt: -1 });
};

applicationQueueSchema.statics.findByListing = function (listingId) {
  return this.find({ listingId }).sort({ createdAt: -1 });
};

applicationQueueSchema.statics.findReadyItems = function (limit = 10) {
  const now = new Date();
  return this.find({
    status: "pending",
    scheduledAt: { $lte: now },
    $or: [{ delayUntil: { $exists: false } }, { delayUntil: { $lte: now } }],
  })
    .sort({ priority: -1, scheduledAt: 1 })
    .limit(limit);
};

applicationQueueSchema.statics.findRetryableItems = function () {
  return this.find({
    status: "failed",
    attempts: { $lt: this.maxAttempts },
    $or: [
      { delayUntil: { $exists: false } },
      { delayUntil: { $lte: new Date() } },
    ],
  }).sort({ priority: -1, updatedAt: 1 });
};

applicationQueueSchema.statics.findExpiredItems = function () {
  const expiryTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.find({
    createdAt: { $lt: expiryTime },
    status: { $in: ["pending", "processing"] },
  });
};

applicationQueueSchema.statics.getUserQueuePosition = function (
  userId,
  itemId
) {
  return this.countDocuments({
    _id: { $ne: itemId },
    userId,
    status: "pending",
    $or: [
      { priority: { $gt: this.priority } },
      {
        priority: this.priority,
        scheduledAt: { $lt: this.scheduledAt },
      },
    ],
  });
};

// Instance methods
applicationQueueSchema.methods.updateStatus = function (
  newStatus,
  error = null
) {
  this.status = newStatus;

  if (error) {
    this.errors.push({
      message: error.message,
      code: error.code || "UNKNOWN",
      attempt: this.attempts,
      stack: error.stack,
      category: error.category || "system",
    });
  }

  return this.save();
};

applicationQueueSchema.methods.incrementAttempt = function () {
  this.attempts += 1;

  // Calculate exponential backoff delay
  const baseDelay = 5 * 60 * 1000; // 5 minutes
  const backoffDelay = baseDelay * Math.pow(2, this.attempts - 1);
  const jitter = Math.random() * 0.1 * backoffDelay; // Add 10% jitter

  this.delayUntil = new Date(Date.now() + backoffDelay + jitter);
  this.status = "retrying";

  return this.save();
};

applicationQueueSchema.methods.scheduleRetry = function (delayMinutes = null) {
  if (this.attempts >= this.maxAttempts) {
    this.status = "failed";
    return this.save();
  }

  const delay = delayMinutes
    ? delayMinutes * 60 * 1000
    : this.calculateBackoffDelay();
  this.delayUntil = new Date(Date.now() + delay);
  this.status = "retrying";

  return this.save();
};

applicationQueueSchema.methods.calculateBackoffDelay = function () {
  const baseDelay = 5 * 60 * 1000; // 5 minutes
  const backoffDelay = baseDelay * Math.pow(2, this.attempts);
  const jitter = Math.random() * 0.1 * backoffDelay;
  return backoffDelay + jitter;
};

applicationQueueSchema.methods.pause = function (reason) {
  this.status = "paused";
  this.metadata.pauseReason = reason;
  return this.save();
};

applicationQueueSchema.methods.resume = function () {
  if (this.status === "paused") {
    this.status = "pending";
    this.metadata.pauseReason = undefined;
  }
  return this.save();
};

applicationQueueSchema.methods.cancel = function (reason) {
  this.status = "cancelled";
  this.metadata.cancelReason = reason;
  this.completedAt = new Date();
  return this.save();
};

applicationQueueSchema.methods.updateMetadata = function (metadata) {
  this.metadata = { ...this.metadata, ...metadata };
  return this.save();
};

applicationQueueSchema.methods.addScreenshot = function (screenshotPath) {
  if (!this.metadata.screenshots) {
    this.metadata.screenshots = [];
  }
  this.metadata.screenshots.push(screenshotPath);
  return this.save();
};

applicationQueueSchema.methods.recordAntiDetectionMeasure = function (
  type,
  data
) {
  if (!this.antiDetection) {
    this.antiDetection = {};
  }

  switch (type) {
    case "fingerprint":
      this.antiDetection.fingerprintUsed = data;
      break;
    case "delay":
      if (!this.antiDetection.delaysApplied) {
        this.antiDetection.delaysApplied = [];
      }
      this.antiDetection.delaysApplied.push(data);
      break;
    case "human_behavior":
      this.antiDetection.humanBehaviorSimulated = true;
      break;
    case "captcha":
      this.antiDetection.captchaEncountered = true;
      break;
    case "blocking":
      this.antiDetection.blockingDetected = true;
      break;
  }

  return this.save();
};

applicationQueueSchema.methods.getEstimatedProcessingTime = function () {
  // Base processing time estimate
  let estimatedTime = 5 * 60 * 1000; // 5 minutes base

  // Add delay for retry attempts
  if (this.attempts > 0) {
    estimatedTime += this.calculateBackoffDelay();
  }

  // Add random delay
  estimatedTime += this.randomDelay;

  // Add queue position delay (rough estimate)
  const queuePosition = this.priority || 0;
  estimatedTime += queuePosition * 2 * 60 * 1000; // 2 minutes per position

  return estimatedTime;
};

// Ensure virtual fields are serialized
applicationQueueSchema.set("toJSON", { virtuals: true });
applicationQueueSchema.set("toObject", { virtuals: true });

module.exports = mongoose.model("ApplicationQueue", applicationQueueSchema);
