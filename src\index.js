const express = require("express");
const mongoose = require("mongoose");
const schedule = require("node-schedule");
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const path = require("path");

const config = require("./config/config");
const { connectDB } = require("./config/database");
const authRoutes = require("./routes/auth");
const scraperRoutes = require("./routes/scraper");
const listingRoutes = require("./routes/listing");
const {
  scrapePararius,
  scrapeFunda,
  scrapeHuurwoningen,
  cleanup,
  getScraperStatus,
} = require("./services/scraper");
const { loggers, requestLogger } = require("./services/logger");
const cacheService = require("./services/cacheService");

// Import middleware
const { globalError<PERSON><PERSON><PERSON>, AppError } = require("./middleware/errorHandler");
const { generalLimiter } = require("./middleware/rateLimiter");

const app = express();
const port = config.port;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'", "data:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'"]
    }
  }
})); // Set security headers with CSP for dashboard
app.use(
  cors({
    origin: config.corsOrigin,
    credentials: true,
  })
);

// Rate limiting
app.use(generalLimiter);

// Logging middleware - use our custom logger
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Connect to database
connectDB();

// Swagger definition
const swaggerOptions = {
  swaggerDefinition: {
    openapi: "3.0.0",
    info: {
      title: "ZakMakelaar API Documentation",
      version: "1.0.0",
      description:
        "Real Estate Listings API with advanced search, caching, and monitoring capabilities",
      contact: {
        name: "ZakMakelaar API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: "Development server",
      },
    ],
    tags: [
      {
        name: "System",
        description: "System health and status endpoints",
      },
      {
        name: "Listings",
        description: "Property listings management and search",
      },
      {
        name: "Search",
        description: "Advanced search functionality and suggestions",
      },
      {
        name: "Authentication",
        description: "User authentication and profile management",
      },
      {
        name: "Scraper",
        description: "Web scraping operations for property data",
      },
      {
        name: "Agent",
        description: "AI Agent management and monitoring",
      },
      {
        name: "AI",
        description:
          "AI-powered features including matching, analysis, and automation",
      },
      {
        name: "Monitoring",
        description: "System monitoring and performance metrics",
      },
      {
        name: "Documents",
        description: "Document vault for secure file upload and management",
      },
      {
        name: "Property Owner",
        description: "Property owner registration, verification, and profile management",
      },
      {
        name: "Admin",
        description: "Administrative functions and user management",
      },
      {
        name: "Auto Application",
        description: "Automated property application system with queue management and form automation",
      },
      {
        name: "Anti Detection",
        description: "Browser stealth and anti-detection system for automated applications",
      },
      {
        name: "Learning & Optimization",
        description: "Machine learning insights and optimization recommendations for application success",
      },
      {
        name: "GDPR Compliance",
        description: "GDPR compliance features including consent management, data export, and privacy controls",
      },
      {
        name: "Notifications",
        description: "Multi-channel notification system for auto-application events, daily summaries, and urgent alerts",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            _id: { type: "string" },
            email: { type: "string", format: "email" },
            role: {
              type: "string",
              enum: ["user", "admin"],
              default: "user",
            },
            preferences: {
              type: "object",
              properties: {
                location: { type: "string" },
                budget: { type: "number" },
                rooms: { type: "number" },
                propertyType: {
                  type: "string",
                  enum: ["apartment", "house", "studio", "room", "any"],
                },
                minSize: { type: "number" },
                maxSize: { type: "number" },
                interior: {
                  type: "string",
                  enum: ["kaal", "gestoffeerd", "gemeubileerd", "any"],
                },
                parking: { type: "boolean" },
                balcony: { type: "boolean" },
                garden: { type: "boolean" },
                furnished: { type: "boolean" },
                petsAllowed: { type: "boolean" },
                smokingAllowed: { type: "boolean" },
                studentFriendly: { type: "boolean" },
                expatFriendly: { type: "boolean" },
                commuteTime: { type: "number" },
                preferredNeighborhoods: {
                  type: "array",
                  items: { type: "string" },
                },
                excludedNeighborhoods: {
                  type: "array",
                  items: { type: "string" },
                },
              },
            },
            aiSettings: {
              type: "object",
              properties: {
                matchThreshold: { type: "number", default: 70 },
                alertFrequency: {
                  type: "string",
                  enum: ["immediate", "hourly", "daily"],
                  default: "immediate",
                },
                preferredLanguage: {
                  type: "string",
                  enum: ["dutch", "english"],
                  default: "english",
                },
                includeMarketAnalysis: { type: "boolean", default: true },
                includeContractAnalysis: { type: "boolean", default: true },
                autoGenerateApplications: { type: "boolean", default: false },
                applicationTemplate: {
                  type: "string",
                  enum: ["professional", "casual", "student", "expat"],
                  default: "professional",
                },
              },
            },
            createdAt: { type: "string", format: "date-time" },
            lastActive: { type: "string", format: "date-time" },
          },
        },
        Listing: {
          type: "object",
          properties: {
            _id: { type: "string" },
            title: { type: "string" },
            price: { 
              type: "string",
              description: "Price of the property, formatted as '€ X.XXX per maand' with proper handling of European number formats including thousands separators",
              example: "€ 3.950 per maand"
            },
            location: { type: "string" },
            url: { type: "string" },
            propertyType: { type: "string" },
            size: { type: "string" },
            rooms: { type: "string" },
            year: { type: "string" },
            interior: { type: "string" },
            source: { type: "string" },
            description: { type: "string" },
            timestamp: { type: "string", format: "date-time" },
          },
        },
        AgentStatus: {
          type: "object",
          properties: {
            isActive: { type: "boolean" },
            lastActivity: { type: "string", format: "date-time" },
            currentTask: { type: "string" },
            autonomyLevel: { type: "number", minimum: 0, maximum: 100 },
            performance: {
              type: "object",
              properties: {
                totalApplications: { type: "number" },
                successRate: { type: "number" },
                averageResponseTime: { type: "number" },
                autonomyRate: { type: "number" },
                userSatisfactionScore: { type: "number" },
              },
            },
            lastUpdated: { type: "string", format: "date-time" },
          },
        },
        AgentMetrics: {
          type: "object",
          properties: {
            totalScrapes: { type: "number" },
            successfulScrapes: { type: "number" },
            failedScrapes: { type: "number" },
            successRate: { type: "number" },
            averageScrapingTime: { type: "number" },
            uptime: { type: "number" },
            lastScrapeTime: { type: "string", format: "date-time" },
            autonomyLevel: { type: "number" },
            autonomyRate: { type: "number" },
          },
        },
        AutoApplicationSettings: {
          type: "object",
          required: ["userId", "isEnabled"],
          properties: {
            _id: { type: "string", description: "Unique identifier for the settings" },
            userId: { type: "string", description: "User ID who owns these settings" },
            isEnabled: { type: "boolean", description: "Whether auto-application is enabled", default: false },
            maxApplicationsPerDay: { type: "number", description: "Maximum number of applications per day", default: 5, minimum: 1, maximum: 20 },
            delayBetweenApplications: { type: "number", description: "Delay between applications in minutes", default: 30, minimum: 5, maximum: 1440 },
            targetCriteria: {
              type: "object",
              properties: {
                maxPrice: { type: "number", description: "Maximum price in euros" },
                minRooms: { type: "number", description: "Minimum number of rooms" },
                maxRooms: { type: "number", description: "Maximum number of rooms" },
                propertyTypes: { type: "array", items: { type: "string", enum: ["apartment", "house", "studio", "room"] }, description: "Preferred property types" },
                locations: { type: "array", items: { type: "string" }, description: "Preferred locations/cities" },
                excludeLocations: { type: "array", items: { type: "string" }, description: "Locations to exclude" }
              }
            },
            applicationTemplate: {
              type: "object",
              properties: {
                personalInfo: {
                  type: "object",
                  properties: {
                    firstName: { type: "string" },
                    lastName: { type: "string" },
                    email: { type: "string", format: "email" },
                    phone: { type: "string" },
                    dateOfBirth: { type: "string", format: "date" }
                  }
                },
                employment: {
                  type: "object",
                  properties: {
                    status: { type: "string", enum: ["employed", "self-employed", "student", "unemployed"] },
                    employer: { type: "string" },
                    position: { type: "string" },
                    monthlyIncome: { type: "number" }
                  }
                },
                preferences: {
                  type: "object",
                  properties: {
                    moveInDate: { type: "string", format: "date" },
                    leaseDuration: { type: "number", description: "Preferred lease duration in months" },
                    petsAllowed: { type: "boolean" },
                    smokingAllowed: { type: "boolean" }
                  }
                },
                customMessage: { type: "string", description: "Custom message to include in applications" }
              }
            },
            createdAt: { type: "string", format: "date-time" },
            updatedAt: { type: "string", format: "date-time" }
          }
        },
        ApplicationQueue: {
          type: "object",
          properties: {
            _id: { type: "string" },
            userId: { type: "string" },
            listingId: { type: "string" },
            listingUrl: { type: "string" },
            status: { type: "string", enum: ["pending", "processing", "completed", "failed", "cancelled"] },
            priority: { type: "number", minimum: 1, maximum: 10 },
            scheduledFor: { type: "string", format: "date-time" },
            attempts: { type: "number", default: 0 },
            maxAttempts: { type: "number", default: 3 },
            lastAttempt: { type: "string", format: "date-time" },
            errorMessage: { type: "string" },
            createdAt: { type: "string", format: "date-time" },
            updatedAt: { type: "string", format: "date-time" }
          }
        },
        ApplicationResult: {
          type: "object",
          properties: {
            _id: { type: "string" },
            userId: { type: "string" },
            queueId: { type: "string" },
            listingId: { type: "string" },
            listingUrl: { type: "string" },
            status: { type: "string", enum: ["success", "failed", "cancelled"] },
            submittedAt: { type: "string", format: "date-time" },
            responseTime: { type: "number", description: "Response time in milliseconds" },
            formData: { type: "object", description: "Data that was submitted in the form" },
            screenshots: { type: "array", items: { type: "string" }, description: "Base64 encoded screenshots of the application process" },
            errorDetails: {
              type: "object",
              properties: {
                message: { type: "string" },
                stack: { type: "string" },
                captchaDetected: { type: "boolean" },
                blockingDetected: { type: "boolean" }
              }
            },
            createdAt: { type: "string", format: "date-time" }
          }
        },
        AutoApplicationStats: {
          type: "object",
          properties: {
            totalApplications: { type: "number" },
            successfulApplications: { type: "number" },
            failedApplications: { type: "number" },
            pendingApplications: { type: "number" },
            successRate: { type: "number", description: "Success rate as a percentage" },
            averageResponseTime: { type: "number", description: "Average response time in milliseconds" },
            applicationsToday: { type: "number" },
            applicationsThisWeek: { type: "number" },
            applicationsThisMonth: { type: "number" },
            lastApplicationDate: { type: "string", format: "date-time" }
          }
        },
        AntiDetectionStats: {
          type: "object",
          properties: {
            activeSessions: { type: "number", description: "Number of active browser sessions" },
            totalPageViews: { type: "number", description: "Total page views across all sessions" },
            oldestSession: { type: "number", description: "Timestamp of the oldest active session" },
            profiles: { type: "number", description: "Number of available stealth profiles" },
            captchaDetections: { type: "number", description: "Total CAPTCHA detections" },
            blockingDetections: { type: "number", description: "Total blocking detections" },
            adaptationCount: { type: "number", description: "Number of adaptations made to new measures" }
          }
        },
        StealthProfile: {
          type: "object",
          properties: {
            userAgent: { type: "string", description: "Browser user agent string" },
            viewport: {
              type: "object",
              properties: {
                width: { type: "number" },
                height: { type: "number" }
              }
            },
            languages: { type: "array", items: { type: "string" }, description: "Browser language preferences" },
            timezone: { type: "string", description: "Browser timezone" },
            platform: { type: "string", description: "Operating system platform" },
            hardwareConcurrency: { type: "number", description: "Number of CPU cores" },
            deviceMemory: { type: "number", description: "Device memory in GB" }
          }
        },
        Error: {
          type: "object",
          properties: {
            status: { type: "string", example: "error" },
            message: { type: "string" },
            statusCode: { type: "number" },
          },
        },
      },
    },
  },
  apis: ["./src/routes/*.js"], // Path to the API docs
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// All routes enabled
app.use("/api/auth", authRoutes);
app.use("/api/scraper", scraperRoutes);
app.use("/api", listingRoutes);
app.use("/api/ai", require("./routes/ai"));
app.use("/api/agent", require("./routes/agent"));
app.use("/api/monitoring", require("./routes/monitoring"));
app.use("/api/monitoring/transformation", require("./routes/transformationMonitoring"));
app.use("/api/monitoring/performance", require("./routes/performanceMonitoring"));
app.use("/api/documents", require("./routes/documents"));
app.use("/api/social-matching", require("./routes/socialMatching"));
app.use("/api/property-owner", require("./routes/propertyOwner"));
app.use("/api/auto-application", require("./routes/autoApplication"));
app.use("/api/anti-detection", require("./routes/antiDetection"));
app.use("/api/user-profile", require("./routes/userProfile"));
app.use("/api/rate-limiting", require("./routes/rateLimiting"));
app.use("/api/learning-optimization", require("./routes/learningOptimization"));
app.use("/api/gdpr", require("./routes/gdprCompliance"));
app.use("/api/notifications", require("./routes/notifications"));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "../public")));

// Serve uploaded images
app.use('/uploads', express.static(path.join(__dirname, "../uploads")));

// Monitoring dashboard routes
app.get("/monitoring", (req, res) => {
  res.sendFile(path.join(__dirname, "../public/monitoring-dashboard.html"));
});

app.get("/transformation-monitoring", (req, res) => {
  res.sendFile(path.join(__dirname, "../public/transformation-dashboard.html"));
});

app.get("/performance-monitoring", (req, res) => {
  res.sendFile(path.join(__dirname, "../public/performance-dashboard.html"));
});

// Health check endpoint
/**
 * @swagger
 * /:
 *   get:
 *     summary: API status and information
 *     tags: [System]
 *     responses:
 *       200:
 *         description: API is running successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: ZakMakelaar API is running!
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 *                 environment:
 *                   type: string
 *                   example: development
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Current server timestamp
 */
app.get("/", (req, res) => {
  res.json({
    status: "success",
    message: "ZakMakelaar API is running!",
    version: "1.0.0",
    environment: config.nodeEnv,
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Comprehensive health check
 *     description: Returns detailed health information about all system components including database, cache, and services
 *     tags: [System]
 *     responses:
 *       200:
 *         description: System health information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 uptime:
 *                   type: number
 *                   description: Server uptime in seconds
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Current server timestamp
 *                 database:
 *                   type: string
 *                   enum: [connected, disconnected]
 *                   description: MongoDB connection status
 *                 cache:
 *                   type: string
 *                   enum: [connected, disconnected]
 *                   description: Redis cache connection status
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: boolean
 *                       description: Database service availability
 *                     cache:
 *                       type: boolean
 *                       description: Cache service availability
 *                     scraper:
 *                       type: boolean
 *                       description: Scraper service availability
 *       500:
 *         description: Health check failed
 */
app.get("/health", async (req, res) => {
  const cacheStats = await cacheService.getStats();

  res.json({
    status: "healthy",
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    database:
      mongoose.connection.readyState === 1 ? "connected" : "disconnected",
    cache: cacheStats ? "connected" : "disconnected",
    services: {
      database: mongoose.connection.readyState === 1,
      cache: cacheStats !== null,
      scraper: true, // Always true if server is running
    },
  });
});

// Handle undefined routes
app.all("*", (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

// Schedule the scraper to run at configured intervals
const scrapingInterval = `*/${config.scrapingIntervalMinutes} * * * *`;
schedule.scheduleJob(scrapingInterval, async () => {
  const startTime = Date.now();
  loggers.scraper.info(
    `Starting scheduled scraping (every ${config.scrapingIntervalMinutes} minutes)`
  );

  try {
    // Check which scrapers are enabled
    const scraperStatus = getScraperStatus();
    const activeScrapers = scraperStatus.activeScrapers || [];
    
    if (activeScrapers.length === 0) {
      loggers.scraper.info("All scrapers are disabled, skipping scheduled scraping");
      return;
    }
    
    loggers.scraper.info(`Running enabled scrapers: ${activeScrapers.join(', ')}`);
    
    // Run only enabled scrapers
    const scraperPromises = [];
    const scraperNames = [];
    
    if (activeScrapers.includes('funda')) {
      scraperPromises.push(scrapeFunda());
      scraperNames.push('funda');
    }
    
    if (activeScrapers.includes('pararius')) {
      scraperPromises.push(scrapePararius());
      scraperNames.push('pararius');
    }
    
    if (activeScrapers.includes('huurwoningen')) {
      scraperPromises.push(scrapeHuurwoningen());
      scraperNames.push('huurwoningen');
    }
    
    const results = await Promise.allSettled(scraperPromises);

    let totalListings = 0;
    
    // Process results dynamically based on which scrapers ran
    results.forEach((result, index) => {
      const scraperName = scraperNames[index];
      
      if (result.status === "fulfilled") {
        totalListings += result.value?.length || 0;
        loggers.scraper.info(`${scraperName} scraping completed`, {
          listings: result.value?.length || 0
        });
      } else {
        loggers.scraper.error(`${scraperName} scraping failed`, {
          error: result.reason,
        });
      }
    });

    const result = { length: totalListings };
    const duration = Date.now() - startTime;
    loggers.scraper.info("Scheduled scraping completed", {
      duration: `${duration}ms`,
      listingsProcessed: result?.length || 0,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    loggers.scraper.error("Scheduled scraping failed", {
      duration: `${duration}ms`,
      error: error.message,
      stack: error.stack,
    });
  }
});

// Schedule periodic processing of existing listings for auto-application
// This ensures applications are created even when no new listings are scraped
if (config.autoApplicationPeriodicProcessing) {
  const intervalPattern = `*/${config.autoApplicationProcessingIntervalMinutes} * * * *`;
  loggers.app.info(`Scheduling periodic auto-application processing every ${config.autoApplicationProcessingIntervalMinutes} minutes`);
  
  schedule.scheduleJob(intervalPattern, async () => {
    const startTime = Date.now();
    loggers.app.info('Starting periodic auto-application processing for existing listings');
    
    try {
      const autoApplicationService = require('./services/autoApplicationService');
      
      // Process existing listings from the last 3 days
      const result = await autoApplicationService.processExistingListings(null, {
        limit: 100,
        daysBack: 3,
        skipRecentlyProcessed: true,
        minQualityScore: 0.6,
      });
      
      const duration = Date.now() - startTime;
      loggers.app.info('Periodic auto-application processing completed', {
        duration: `${duration}ms`,
        applicationsCreated: result.applicationsCreated,
        usersProcessed: result.usersProcessed,
        listingsProcessed: result.listingsFound,
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      loggers.app.error('Periodic auto-application processing failed', {
        duration: `${duration}ms`,
        error: error.message,
        stack: error.stack,
      });
    }
  });
} else {
  loggers.app.info('Periodic auto-application processing is disabled');
}

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  console.log(`👋 ${signal} RECEIVED. Shutting down gracefully`);

  try {
    // Cleanup scraper resources
    await cleanup();

    // Close WebSocket service
    const websocketService = require('./services/websocketService');
    websocketService.close();

    // Shutdown notification services
    const notificationScheduler = require('./services/notificationScheduler');
    const autoApplicationNotificationService = require('./services/autoApplicationNotificationService');
    notificationScheduler.shutdown();
    autoApplicationNotificationService.shutdown();

    // Shutdown auto-application service
    const autoApplicationService = require('./services/autoApplicationService');
    autoApplicationService.shutdown();

    // Close server
    server.close(() => {
      console.log("💥 Process terminated!");
      process.exit(0);
    });
  } catch (error) {
    console.error("Error during graceful shutdown:", error);
    process.exit(1);
  }
};

process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

process.on("unhandledRejection", async (err) => {
  console.log("UNHANDLED REJECTION! 💥 Shutting down...");
  loggers.app.error("Unhandled Promise Rejection", { error: err.message, stack: err.stack });

  try {
    await cleanup();
  } catch (cleanupError) {
    loggers.app.error("Error during cleanup", { error: cleanupError });
  }

  server.close(() => {
    process.exit(1);
  });
});

const server = app.listen(port, () => {
  loggers.app.info("🚀 ZakMakelaar API Server Started", {
    port,
    environment: config.nodeEnv,
    endpoints: {
      api: `http://localhost:${port}`,
      docs: `http://localhost:${port}/api-docs`,
      health: `http://localhost:${port}/health`,
    },
  });

  // Initialize WebSocket service
  const websocketService = require('./services/websocketService');
  websocketService.initialize(server);

  // Initialize notification scheduler
  const notificationScheduler = require('./services/notificationScheduler');
  notificationScheduler.start();

  // Initialize auto-application service
  const autoApplicationService = require('./services/autoApplicationService');
  // Start the auto-application processing loop
  autoApplicationService._startProcessing();
  loggers.app.info('Auto-application service initialized and started');
  
  // Process existing listings immediately on startup (configurable)
  if (config.autoApplicationProcessOnStartup) {
    setTimeout(async () => {
      try {
        loggers.app.info('Running initial auto-application processing for existing listings');
        const result = await autoApplicationService.processExistingListings(null, {
          limit: 50,
          daysBack: 3,
          skipRecentlyProcessed: true,
          minQualityScore: 0.6,
        });
        
        loggers.app.info('Initial auto-application processing completed', {
          applicationsCreated: result.applicationsCreated,
          usersProcessed: result.usersProcessed,
          listingsProcessed: result.listingsFound,
        });
      } catch (error) {
        loggers.app.error('Initial auto-application processing failed', {
          error: error.message,
        });
      }
    }, 5000); // Run after 5 seconds to allow server to fully initialize
  } else {
    loggers.app.info('Startup auto-application processing is disabled');
  }

  // Console output for development
  if (config.nodeEnv === "development") {
    console.log(`🚀 Server is running on port ${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api-docs`);
    console.log(`🏥 Health Check: http://localhost:${port}/health`);
    console.log(`🔌 WebSocket: ws://localhost:${port}`);
    console.log(`🌍 Environment: ${config.nodeEnv}`);
  }
});
