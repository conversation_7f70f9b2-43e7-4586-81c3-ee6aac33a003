param(
  [string]$PropertyUrl = "https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/",
  [string]$FirstName = "Wellis",
  [string]$LastName = "Hant",
  [string]$Email = "<EMAIL>",
  [string]$Phone = "030 686 62 00",
  [string]$Message = ""
)

$ErrorActionPreference = 'Stop'

$img = "zakmakelaar-funda:latest"

Write-Host "Building Docker image $img ..."
$dockerfile = Join-Path $PSScriptRoot 'Dockerfile.funda'
$context = Join-Path $PSScriptRoot '..'
docker build -f $dockerfile -t $img $context

$artifacts = Join-Path $PSScriptRoot 'artifacts'
if (!(Test-Path $artifacts)) { New-Item -ItemType Directory -Path $artifacts | Out-Null }

Write-Host "Running container..."
docker run --rm `
  -e "PROPERTY_URL=$PropertyUrl" `
  -e "FIRST_NAME=$FirstName" `
  -e "LAST_NAME=$LastName" `
  -e "EMAIL=$Email" `
  -e "PHONE=$Phone" `
  -e "MESSAGE=$Message" `
  -v "${artifacts}:/app/zakmakelaar-backend/artifacts" `
  $img

Write-Host "Run completed. Check artifacts folder for screenshots."
